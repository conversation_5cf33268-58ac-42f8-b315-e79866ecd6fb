<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_YqK8EIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_YqK8EYfDEfCdj8Tp72PBqA" bindingContexts="_YqLALofDEfCdj8Tp72PBqA">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_lvds.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_app.h&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_app.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_app.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.make.editor&quot; name=&quot;Makefile&quot; tooltip=&quot;UVC-UAC_250722/Makefile&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/Makefile&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_gpif_header_lvds.h&quot; tooltip=&quot;UVC-UAC_250722/cy_gpif_header_lvds.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_gpif_header_lvds.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;UVC-UAC_250722/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_lvds.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_app.c&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_qspi.c&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_qspi.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_qspi.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_i2c.c&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_i2c.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_i2c.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_qspi.h&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_qspi.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_qspi.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_debug.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_debug.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_debug.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_debug.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_debug.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_debug.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_syslib.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_syslib.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_syslib.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_gpio.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_gpio.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_gpio.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;gpio_fx3g2_169_bga.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/gpio_fx3g2_169_bga.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/gpio_fx3g2_169_bga.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_usbd.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usb_usbd.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usb_usbd.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.ide.FileStoreEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;_stdint.h&quot; tooltip=&quot;C:\Users\<USER>\Infineon\Tools\mtb-gcc-arm-eabi\11.3.1\gcc\arm-none-eabi\include\sys\_stdint.h&quot;>&#xD;&#xA;&lt;persistable uri=&quot;file:/C:/Users/<USER>/Infineon/Tools/mtb-gcc-arm-eabi/11.3.1/gcc/arm-none-eabi/include/sys/_stdint.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_hbdma_mgr.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma_mgr.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma_mgr.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_usbd.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usb_usbd.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usb_usbd.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usbss_cal_drv.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usbss_cal_drv.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usbss_cal_drv.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usbss_cal_drv.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usbss_cal_drv.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usbss_cal_drv.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usbhs_cal_drv.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usbhs_cal_drv.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usbhs_cal_drv.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cyusb4024_bzxi.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/cyusb4024_bzxi.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/cyusb4024_bzxi.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_sysint.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_sysint.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_sysint.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_syslib.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_syslib.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_syslib.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_common.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usb_common.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_usb_common/cy_usb_common.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;core_cm4.h&quot; tooltip=&quot;mtb_shared/cmsis/release-v5.8.2/Core/Include/core_cm4.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/cmsis/release-v5.8.2/Core/Include/core_cm4.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cmsis_gcc.h&quot; tooltip=&quot;mtb_shared/cmsis/release-v5.8.2/Core/Include/cmsis_gcc.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/cmsis/release-v5.8.2/Core/Include/cmsis_gcc.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cybsp.c&quot; tooltip=&quot;UVC-UAC_250722/bsps/TARGET_APP_KIT_FX20_FMC_001/cybsp.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/bsps/TARGET_APP_KIT_FX20_FMC_001/cybsp.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_descriptors.c&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_descriptors.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_descriptors.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;timers.c&quot; tooltip=&quot;mtb_shared/freertos/release-v10.5.004/Source/timers.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/freertos/release-v10.5.004/Source/timers.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;queue.c&quot; tooltip=&quot;mtb_shared/freertos/release-v10.5.004/Source/queue.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/freertos/release-v10.5.004/Source/queue.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;FreeRTOS.h&quot; tooltip=&quot;mtb_shared/freertos/release-v10.5.004/Source/include/FreeRTOS.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/freertos/release-v10.5.004/Source/include/FreeRTOS.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;queue.h&quot; tooltip=&quot;mtb_shared/freertos/release-v10.5.004/Source/include/queue.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/freertos/release-v10.5.004/Source/include/queue.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_uvc_device.h&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_uvc_device.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_uvc_device.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usbhs_dw_wrapper.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_usbhs_dw_wrapper.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_usbhs_dw_wrapper.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_hbdma_mgr.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma_mgr.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma_mgr.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_hbdma.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_hbdma_dscr_list.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma_dscr_list.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/hb_dma/cy_hbdma_dscr_list.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.ide.FileStoreEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;stdbool.h&quot; tooltip=&quot;C:\Users\<USER>\Infineon\Tools\mtb-gcc-arm-eabi\11.3.1\gcc\lib\gcc\arm-none-eabi\11.3.1\include\stdbool.h&quot;>&#xD;&#xA;&lt;persistable uri=&quot;file:/C:/Users/<USER>/Infineon/Tools/mtb-gcc-arm-eabi/11.3.1/gcc/lib/gcc/arm-none-eabi/11.3.1/include/stdbool.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.mylyn.wikitext.ui.editor.markupEditor&quot; name=&quot;README.md&quot; tooltip=&quot;UVC-UAC_250722/README.md&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/README.md&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_uac_app.c&quot; tooltip=&quot;UVC-UAC_250722/cy_uac_app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_uac_app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_pdm_pcm_v2.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_pdm_pcm_v2.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_pdm_pcm_v2.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cyip_lvdsss.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/ip/cyip_lvdsss.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/ip/cyip_lvdsss.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_device.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/source/cy_device.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/source/cy_device.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;FreeRTOSConfig.h&quot; tooltip=&quot;UVC-UAC_250722/FreeRTOSConfig.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/FreeRTOSConfig.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_i2c.h&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_i2c.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_usb_i2c.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_uart.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_uart.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_uart.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_gpif_header_lvcmos.h&quot; tooltip=&quot;UVC-UAC_250722/cy_gpif_header_lvcmos.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UVC-UAC_250722/cy_gpif_header_lvcmos.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_app.h&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_usb_app.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_usb_app.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_lvds.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.2.1/sources/lvds_lvcmos/cy_lvds.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.2.1/sources/lvds_lvcmos/cy_lvds.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_i2c.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_usb_i2c.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_usb_i2c.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_app.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_usb_app.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_usb_app.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_qspi.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_usb_qspi.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_usb_qspi.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;fx3g2_config.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/fx3g2_config.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/devices/COMPONENT_CAT1A/include/fx3g2_config.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_qspi.h&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_usb_qspi.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_usb_qspi.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_smif.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_smif.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_smif.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.make.editor&quot; name=&quot;Makefile&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/Makefile&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/Makefile&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.mylyn.wikitext.ui.editor.markupEditor&quot; name=&quot;README.md&quot; tooltip=&quot;USB_Test_App_250722/README.md&quot;>&#xD;&#xA;&lt;persistable path=&quot;/USB_Test_App_250722/README.md&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.mylyn.wikitext.ui.editor.markupEditor&quot; name=&quot;README.md&quot; tooltip=&quot;USBSS_Device_250722/README.md&quot;>&#xD;&#xA;&lt;persistable path=&quot;/USBSS_Device_250722/README.md&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.mylyn.wikitext.ui.editor.markupEditor&quot; name=&quot;README.md&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/README.md&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/README.md&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_sysint.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_sysint.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_sysint.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_i2c.h&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_usb_i2c.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_usb_i2c.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_scb_i2c.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_scb_i2c.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_scb_i2c.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_sysclk.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_sysclk.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_sysclk.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_sysclk.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_sysclk.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_sysclk.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_scb_i2c.h&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_scb_i2c.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/include/cy_scb_i2c.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_utils.h&quot; tooltip=&quot;mtb_shared/core-lib/release-v1.4.4/include/cy_utils.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/core-lib/release-v1.4.4/include/cy_utils.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_gpio.c&quot; tooltip=&quot;mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_gpio.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/mtb-pdl-cat1/release-v3.16.0/drivers/source/cy_gpio.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_usbd.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.2.1/sources/fx_usb_common/cy_usb_usbd.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.2.1/sources/fx_usb_common/cy_usb_usbd.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_debug.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.2.1/sources/fx_utils/cy_debug.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.2.1/sources/fx_utils/cy_debug.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.ide.FileStoreEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;_default_types.h&quot; tooltip=&quot;C:\Users\<USER>\Infineon\Tools\mtb-gcc-arm-eabi\11.3.1\gcc\arm-none-eabi\include\machine\_default_types.h&quot;>&#xD;&#xA;&lt;persistable uri=&quot;file:/C:/Users/<USER>/Infineon/Tools/mtb-gcc-arm-eabi/11.3.1/gcc/arm-none-eabi/include/machine/_default_types.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cybsp.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/bsps/TARGET_APP_KIT_FX20_FMC_001/cybsp.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/bsps/TARGET_APP_KIT_FX20_FMC_001/cybsp.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;app_version.h&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/app_version.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/app_version.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_uart.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.2.1/sources/fx_utils/cy_uart.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.2.1/sources/fx_utils/cy_uart.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_debug.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.2.1/sources/fx_utils/cy_debug.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.2.1/sources/fx_utils/cy_debug.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_gpif_header.h&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cy_gpif_header.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cy_gpif_header.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;fx3g2_descriptors.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/fx3g2_descriptors.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/fx3g2_descriptors.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cm0_code.c&quot; tooltip=&quot;Slave_FIFO_2Bit_250722/cm0_code.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/Slave_FIFO_2Bit_250722/cm0_code.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.browser.editorSupport&quot; name=&quot;group__group__usbfxstack__hb__dma__functions.html&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.2.1/docs/html/group__group__usbfxstack__hb__dma__functions.html&quot;>&#xD;&#xA;&lt;persistable path=&quot;/mtb_shared/usbfxstack/release-v1.2.1/docs/html/group__group__usbfxstack__hb__dma__functions.html&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:com.cypress.studio.app.scheme.modustoolbox</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_YqK8EYfDEfCdj8Tp72PBqA" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_YqK8EofDEfCdj8Tp72PBqA" x="0" y="0" width="1858" height="1056">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1753168430007"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_YqK8EofDEfCdj8Tp72PBqA" selectedElement="_YqK8E4fDEfCdj8Tp72PBqA" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_YqK8E4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_YqK8FIfDEfCdj8Tp72PBqA">
        <children xsi:type="advanced:Perspective" xmi:id="_YqK8FIfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.perspective.ModusToolbox" selectedElement="_YqK8FYfDEfCdj8Tp72PBqA" label="ModusToolbox&#x2122;" iconURI="platform:/plugin/com.cypress.studio.app/icons/config_file_16.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.undo,persp.hideToolbarSC:org.eclipse.ui.edit.redo,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideMenuSC:org.eclipse.cdt.ui.edit.opendecl,persp.hideMenuSC:org.eclipse.cdt.ui.edit.open.type.hierarchy,persp.hideToolbarSC:org.eclipse.cdt.ui.edit.open.call.hierarchy,persp.hideMenuSC:org.eclipse.cdt.ui.navigate.opentype,persp.hideMenuSC:org.eclipse.cdt.ui.edit.open.include.browser,persp.hideMenuSC:org.eclipse.cdt.ui.navigate.open.type.in.hierarchy,persp.hideMenuSC:org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy,"/>
          <tags>persp.actionSet:org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset</tags>
          <tags>persp.actionSet:org.eclipse.mylyn.tasks.ui.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.text.quicksearch.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.showIn:org.eclipse.tm.terminal.view.ui.TerminalsView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.viewSC:org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView</tags>
          <tags>persp.viewSC:com.cypress.studio.views.quickpanel</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_YqK8FYfDEfCdj8Tp72PBqA" selectedElement="_YqK8I4fDEfCdj8Tp72PBqA" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_YqK8FofDEfCdj8Tp72PBqA" containerData="2181" selectedElement="_YqK8F4fDEfCdj8Tp72PBqA">
              <children xsi:type="basic:PartStack" xmi:id="_YqK8F4fDEfCdj8Tp72PBqA" elementId="topLeft" containerData="5804" selectedElement="_YqK8GIfDEfCdj8Tp72PBqA">
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8GIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_YqK90YfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8GYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.DebugView" ref="_YqK964fDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8GofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.RegisterView" ref="_YqK97ofDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8G4fDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" ref="_YqK98YfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8HIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_YqK99IfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:C/C++</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8HYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_YqK99YfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_YqK8HofDEfCdj8Tp72PBqA" elementId="bottomLeft" containerData="4196" selectedElement="_YqK8H4fDEfCdj8Tp72PBqA">
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8H4fDEfCdj8Tp72PBqA" elementId="com.cypress.studio.views.quickpanel" ref="_YqK99ofDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Other</tags>
                  <tags>NoClose</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8IIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.VariableView" ref="_YqK9-YfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8IYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.ExpressionView" ref="_YqK9_IfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8IofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.BreakpointView" ref="_YqK9_YfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_YqK8I4fDEfCdj8Tp72PBqA" containerData="7819" selectedElement="_YqK8JIfDEfCdj8Tp72PBqA">
              <children xsi:type="basic:PartSashContainer" xmi:id="_YqK8JIfDEfCdj8Tp72PBqA" containerData="7147" selectedElement="_YqK8JYfDEfCdj8Tp72PBqA" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8JYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editorss" containerData="8809" ref="_YqK9uIfDEfCdj8Tp72PBqA"/>
                <children xsi:type="basic:PartStack" xmi:id="_YqK8JofDEfCdj8Tp72PBqA" elementId="sideRight" containerData="1191" selectedElement="_YqK8J4fDEfCdj8Tp72PBqA">
                  <children xsi:type="advanced:Placeholder" xmi:id="_YqK8J4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ContentOutline" ref="_YqK-JYfDEfCdj8Tp72PBqA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                  <children xsi:type="advanced:Placeholder" xmi:id="_YqK8KIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.minimap.MinimapView" toBeRendered="false" ref="_YqK-NYfDEfCdj8Tp72PBqA" closeable="true">
                    <tags>View</tags>
                    <tags>categoryTag:General</tags>
                  </children>
                </children>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_YqK8KYfDEfCdj8Tp72PBqA" elementId="bottom" containerData="2853" selectedElement="_YqK8KofDEfCdj8Tp72PBqA">
                <tags>Language Servers</tags>
                <tags>General</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8KofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView" ref="_YqK9_ofDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8K4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProblemView" ref="_YqK-C4fDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8LIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProgressView" ref="_YqK-DofDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8LYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.MemoryView" ref="_YqK-EYfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Debug</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8LofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" ref="_YqK-FIfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Terminal</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8L4fDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" ref="_YqK-NofDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:Language Servers</tags>
                </children>
                <children xsi:type="advanced:Placeholder" xmi:id="_YqK8MIfDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.views.SearchView" ref="_YqK-OYfDEfCdj8Tp72PBqA" closeable="true">
                  <tags>View</tags>
                  <tags>categoryTag:General</tags>
                </children>
              </children>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_YqK8MYfDEfCdj8Tp72PBqA" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_YqK8MofDEfCdj8Tp72PBqA" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_YqK9tYfDEfCdj8Tp72PBqA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_YqK8M4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_YqK9tofDEfCdj8Tp72PBqA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:General</tags>
        </children>
        <children xsi:type="advanced:Placeholder" xmi:id="_YqK8NIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_YqK9t4fDEfCdj8Tp72PBqA" closeable="true">
          <tags>View</tags>
          <tags>categoryTag:Help</tags>
        </children>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9tYfDEfCdj8Tp72PBqA" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9tofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9t4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_YqK9uIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editorss" selectedElement="_YqK9uYfDEfCdj8Tp72PBqA">
      <children xsi:type="basic:PartStack" xmi:id="_YqK9uYfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_YqK9yYfDEfCdj8Tp72PBqA">
        <tags>EditorStack</tags>
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>active</tags>
        <tags>noFocus</tags>
        <children xsi:type="basic:Part" xmi:id="_YqK9uofDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="main.c" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;main.c&quot; partName=&quot;main.c&quot; title=&quot;main.c&quot; tooltip=&quot;UVC-UAC_250722/main.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UVC-UAC_250722/main.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;47810&quot; selectionTopPixel=&quot;46376&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9vofDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="cy_usb_app.c" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_app.c&quot; partName=&quot;cy_usb_app.c&quot; title=&quot;cy_usb_app.c&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_app.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UVC-UAC_250722/cy_usb_app.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;55099&quot; selectionTopPixel=&quot;45484&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9v4fDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="cy_usb_app.h" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_usb_app.h&quot; partName=&quot;cy_usb_app.h&quot; title=&quot;cy_usb_app.h&quot; tooltip=&quot;UVC-UAC_250722/cy_usb_app.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UVC-UAC_250722/cy_usb_app.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4389&quot; selectionTopPixel=&quot;3672&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9w4fDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="cy_debug.c" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_debug.c&quot; partName=&quot;cy_debug.c&quot; title=&quot;cy_debug.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_debug.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/fx_utils/cy_debug.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;8&quot; selectionOffset=&quot;19315&quot; selectionTopPixel=&quot;19958&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9xIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="cy_lvds.c" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_lvds.c&quot; partName=&quot;cy_lvds.c&quot; title=&quot;cy_lvds.c&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;24&quot; selectionOffset=&quot;71413&quot; selectionTopPixel=&quot;61248&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9xYfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="cy_lvds.h" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_lvds.h&quot; partName=&quot;cy_lvds.h&quot; title=&quot;cy_lvds.h&quot; tooltip=&quot;mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/mtb_shared/usbfxstack/release-v1.3.0/sources/lvds_lvcmos/cy_lvds.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;21&quot; selectionOffset=&quot;9792&quot; selectionTopPixel=&quot;6706&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9yYfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="cy_gpif_header_lvds.h" iconURI="platform:/plugin/org.eclipse.cdt.lsp/icons/c.png" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.lsp.CEditor&quot; name=&quot;cy_gpif_header_lvds.h&quot; partName=&quot;cy_gpif_header_lvds.h&quot; title=&quot;cy_gpif_header_lvds.h&quot; tooltip=&quot;UVC-UAC_250722/cy_gpif_header_lvds.h&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UVC-UAC_250722/cy_gpif_header_lvds.h&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;2&quot; selectionOffset=&quot;5357&quot; selectionTopPixel=&quot;780&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.lsp.CEditor</tags>
          <tags>active</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_YqK9zYfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="Makefile" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/etool16/makefile.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.make.editor&quot; name=&quot;Makefile&quot; partName=&quot;Makefile&quot; title=&quot;Makefile&quot; tooltip=&quot;UVC-UAC_250722/Makefile&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UVC-UAC_250722/Makefile&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;4506&quot; selectionTopPixel=&quot;4080&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>removeOnHide</tags>
          <tags>org.eclipse.cdt.make.editor</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK90YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;0&quot;>&#xD;&#xA;&lt;lastRecentlyUsedFilters/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YqK90ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK95ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK964fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YqK97IfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK97YfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK97ofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YqK974fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK98IfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK98YfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YqK98ofDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK984fDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK99IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK99YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK99ofDEfCdj8Tp72PBqA" elementId="com.cypress.studio.views.quickpanel" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Quick Panel" iconURI="platform:/plugin/com.cypress.studio.app/icons/config_file_16.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.cypress.studio.app.views.quickpanel.CyQuickPanel"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.cypress.studio.app"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Other</tags>
      <menus xmi:id="_YqK994fDEfCdj8Tp72PBqA" elementId="com.cypress.studio.views.quickpanel">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK9-IfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.views.quickpanel"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9-YfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YqK9-ofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK9-4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9_IfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9_YfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK9_ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YqK9_4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-AYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-C4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;120&quot; org.eclipse.ui.ide.markerType=&quot;120&quot; org.eclipse.ui.ide.pathField=&quot;160&quot; org.eclipse.ui.ide.resourceField=&quot;120&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;1770&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YqK-DIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-DYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-DofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YqK-D4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-EIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProgressView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-EYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_YqK-EofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-E4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.MemoryView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-FIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view>&#xD;&#xA;&lt;terminalConnections id=&quot;org.eclipse.tm.terminal.view.ui.TerminalsView&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:Terminal</tags>
      <menus xmi:id="_YqK-FYfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-F4fDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-JYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YqK-JofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-L4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-NYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.minimap.MinimapView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-NofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Language Servers</tags>
      <menus xmi:id="_YqK-N4fDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-OIfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_YqK-OYfDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.views.SearchView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view isPinned=&quot;false&quot;>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;&quot; org.eclipse.search.lastActivation=&quot;0&quot;/>&#xD;&#xA;&lt;view IMemento.internal.id=&quot;org.eclipse.cdt.ui.pdomSearchViewPage&quot; definitionColumnWidth=&quot;150&quot; locationColumnWidth=&quot;300&quot; matchColumnWidth=&quot;300&quot; org.eclipse.search.lastActivation=&quot;1&quot; org.eclipse.search.resultpage.layout=&quot;2&quot; showEnclosingDefinitions=&quot;true&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_YqK-OofDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.views.SearchView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_YqK-O4fDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.views.SearchView" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_YqK-PIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-PYfDEfCdj8Tp72PBqA" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YqK-PofDEfCdj8Tp72PBqA" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-P4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YqK-R4fDEfCdj8Tp72PBqA" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_YqLpbYfDEfCdj8Tp72PBqA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-T4fDEfCdj8Tp72PBqA" elementId="group.edit" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YqK-UIfDEfCdj8Tp72PBqA" elementId="group.edit" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-UYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.workbench.edit">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YqK-U4fDEfCdj8Tp72PBqA" elementId="undo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/undo_edit.png" tooltip="Undo" enabled="false" command="_YqLpT4fDEfCdj8Tp72PBqA"/>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YqK-VIfDEfCdj8Tp72PBqA" elementId="redo" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/redo_edit.png" tooltip="Redo" enabled="false" command="_YqMKnIfDEfCdj8Tp72PBqA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-VYfDEfCdj8Tp72PBqA" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YqK-VofDEfCdj8Tp72PBqA" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-fofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-g4fDEfCdj8Tp72PBqA" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-iIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-kIfDEfCdj8Tp72PBqA" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YqK-kYfDEfCdj8Tp72PBqA" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-kofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_YqK-mIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.pinEditor" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_YqLo6ofDEfCdj8Tp72PBqA"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-nYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-nofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.lsp.CEditor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-n4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.editor" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-oIfDEfCdj8Tp72PBqA" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YqK-oYfDEfCdj8Tp72PBqA" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-oofDEfCdj8Tp72PBqA" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_YqK-o4fDEfCdj8Tp72PBqA" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_YqK-pIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-p4fDEfCdj8Tp72PBqA" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-q4fDEfCdj8Tp72PBqA" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_YqK-sofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-s4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.StatusLine" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-tIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.HeapStatus" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-tYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ProgressBar" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_YqK-tofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-t4fDEfCdj8Tp72PBqA" elementId="topLeft(IDEWindow).(com.cypress.studio.perspective.ModusToolbox)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-uIfDEfCdj8Tp72PBqA" elementId="bottomLeft(IDEWindow).(com.cypress.studio.perspective.ModusToolbox)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_YqK-uYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-uofDEfCdj8Tp72PBqA" elementId="sideRight(IDEWindow).(com.cypress.studio.perspective.ModusToolbox)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_YqK-u4fDEfCdj8Tp72PBqA" elementId="bottom(IDEWindow).(com.cypress.studio.perspective.ModusToolbox)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_YqK-vIfDEfCdj8Tp72PBqA" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_YqLALofDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK-vYfDEfCdj8Tp72PBqA" keySequence="CTRL+1" command="_YqLo3ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-vofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+L" command="_YqLpvYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-v4fDEfCdj8Tp72PBqA" keySequence="CTRL+V" command="_YqLn9YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-wIfDEfCdj8Tp72PBqA" keySequence="CTRL+A" command="_YqMKXofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-wYfDEfCdj8Tp72PBqA" keySequence="CTRL+C" command="_YqLnuofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-wofDEfCdj8Tp72PBqA" keySequence="CTRL+X" command="_YqLpWYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-w4fDEfCdj8Tp72PBqA" keySequence="CTRL+Y" command="_YqMKnIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-xIfDEfCdj8Tp72PBqA" keySequence="CTRL+Z" command="_YqLpT4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-xYfDEfCdj8Tp72PBqA" keySequence="ALT+PAGE_UP" command="_YqMKuYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-xofDEfCdj8Tp72PBqA" keySequence="ALT+PAGE_DOWN" command="_YqLojofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-x4fDEfCdj8Tp72PBqA" keySequence="SHIFT+INSERT" command="_YqLn9YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-yIfDEfCdj8Tp72PBqA" keySequence="ALT+F11" command="_YqLoWofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_YqK-yYfDEfCdj8Tp72PBqA" keySequence="CTRL+F10" command="_YqLoLYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-yofDEfCdj8Tp72PBqA" keySequence="CTRL+INSERT" command="_YqLnuofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-y4fDEfCdj8Tp72PBqA" keySequence="CTRL+PAGE_UP" command="_YqLph4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-zIfDEfCdj8Tp72PBqA" keySequence="CTRL+PAGE_DOWN" command="_YqLo64fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-zYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+F3" command="_YqLpeIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-zofDEfCdj8Tp72PBqA" keySequence="SHIFT+DEL" command="_YqLpWYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-z4fDEfCdj8Tp72PBqA" keySequence="ALT+/" command="_YqLpKofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <tags>locale:zh</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK-0IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.textEditorScope" bindingContext="_YqLAM4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK-0YfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+CR" command="_YqLpdofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-0ofDEfCdj8Tp72PBqA" keySequence="CTRL+BS" command="_YqLnsIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-04fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+Q" command="_YqLoxIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-1IfDEfCdj8Tp72PBqA" keySequence="CTRL+7" command="_YqLny4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-1YfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+J" command="_YqLosofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-1ofDEfCdj8Tp72PBqA" keySequence="CTRL++" command="_YqLoVYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-14fDEfCdj8Tp72PBqA" keySequence="CTRL+-" command="_YqMKMIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-2IfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+C" command="_YqLny4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-2YfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F" command="_YqLpKIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-2ofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+J" command="_YqLo14fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-24fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+A" command="_YqLn5YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-3IfDEfCdj8Tp72PBqA" keySequence="CTRL+T" command="_YqMKgYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-3YfDEfCdj8Tp72PBqA" keySequence="CTRL+J" command="_YqLoNofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-3ofDEfCdj8Tp72PBqA" keySequence="CTRL+L" command="_YqLpSIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-34fDEfCdj8Tp72PBqA" keySequence="CTRL+O" command="_YqLnxofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-4IfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+/" command="_YqLpPIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-4YfDEfCdj8Tp72PBqA" keySequence="CTRL+D" command="_YqLoSofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-4ofDEfCdj8Tp72PBqA" keySequence="CTRL+=" command="_YqLoVYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-44fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+/" command="_YqMKQIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_YqK-5IfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Y" command="_YqLnn4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-5YfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+DEL" command="_YqLpMIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-5ofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+X" command="_YqLnyIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-54fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+Y" command="_YqLpv4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-6IfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+\" command="_YqLoCofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-6YfDEfCdj8Tp72PBqA" keySequence="CTRL+DEL" command="_YqLpSofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-6ofDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_UP" command="_YqMKs4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-64fDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_DOWN" command="_YqLonofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-7IfDEfCdj8Tp72PBqA" keySequence="SHIFT+END" command="_YqMKOYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-7YfDEfCdj8Tp72PBqA" keySequence="SHIFT+HOME" command="_YqLppofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-7ofDEfCdj8Tp72PBqA" keySequence="END" command="_YqLplIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-74fDEfCdj8Tp72PBqA" keySequence="INSERT" command="_YqLoG4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-8IfDEfCdj8Tp72PBqA" keySequence="F2" command="_YqLo7YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-8YfDEfCdj8Tp72PBqA" keySequence="HOME" command="_YqMKPIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-8ofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+ARROW_UP" command="_YqMKg4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-84fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+ARROW_DOWN" command="_YqMKcIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-9IfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+INSERT" command="_YqLolYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-9YfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_YqMKPofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-9ofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_YqLooIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-94fDEfCdj8Tp72PBqA" keySequence="CTRL+F10" command="_YqLpcofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK--IfDEfCdj8Tp72PBqA" keySequence="CTRL+END" command="_YqLoo4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK--YfDEfCdj8Tp72PBqA" keySequence="CTRL+ARROW_UP" command="_YqLocYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK--ofDEfCdj8Tp72PBqA" keySequence="CTRL+ARROW_DOWN" command="_YqMKzofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK--4fDEfCdj8Tp72PBqA" keySequence="CTRL+ARROW_LEFT" command="_YqLnsYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-_IfDEfCdj8Tp72PBqA" keySequence="CTRL+ARROW_RIGHT" command="_YqLowIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-_YfDEfCdj8Tp72PBqA" keySequence="CTRL+HOME" command="_YqLn9IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-_ofDEfCdj8Tp72PBqA" keySequence="CTRL+NUMPAD_MULTIPLY" command="_YqLow4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK-_4fDEfCdj8Tp72PBqA" keySequence="CTRL+NUMPAD_ADD" command="_YqMKaIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_AIfDEfCdj8Tp72PBqA" keySequence="CTRL+NUMPAD_SUBTRACT" command="_YqLpdYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_AYfDEfCdj8Tp72PBqA" keySequence="CTRL+NUMPAD_DIVIDE" command="_YqLodYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_AofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_YqLo1YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_A4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_YqLoHIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_BIfDEfCdj8Tp72PBqA" keySequence="SHIFT+CR" command="_YqMKO4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_BYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_YqLAPIfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_BofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+C" command="_YqMK0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_B4fDEfCdj8Tp72PBqA" keySequence="CTRL+TAB" command="_YqMKyofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_CIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+P" command="_YqLpmYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_CYfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+T" command="_YqLpEofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_CofDEfCdj8Tp72PBqA" keySequence="CTRL+7" command="_YqLn-IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_C4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+H" command="_YqLoPYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_DIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+N" command="_YqLov4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_DYfDEfCdj8Tp72PBqA" keySequence="CTRL+/" command="_YqLn-IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_DofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+O" command="_YqLpcYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_D4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+A" command="_YqLpeYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_EIfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+S" command="_YqMKoYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_EYfDEfCdj8Tp72PBqA" keySequence="CTRL+#" command="_YqLo_4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_EofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+C" command="_YqLn-IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_E4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F" command="_YqMKy4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_FIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+G" command="_YqLnpIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_FYfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+H" command="_YqLo5IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_FofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+I" command="_YqLn_4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_F4fDEfCdj8Tp72PBqA" keySequence="CTRL+T" command="_YqLoqIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_GIfDEfCdj8Tp72PBqA" keySequence="CTRL+I" command="_YqLorofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_GYfDEfCdj8Tp72PBqA" keySequence="CTRL+O" command="_YqLn1IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_GofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+/" command="_YqMKZIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_G4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+R" command="_YqLpO4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_HIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+S" command="_YqLoMIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_HYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+T" command="_YqLpBYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_HofDEfCdj8Tp72PBqA" keySequence="CTRL+G" command="_YqMKlIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_H4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+L" command="_YqMKcofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_IIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+M" command="_YqLn5ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_IYfDEfCdj8Tp72PBqA" keySequence="CTRL+=" command="_YqLo_4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_IofDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+O" command="_YqLpYIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_I4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Z" command="_YqLpYYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_JIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+\" command="_YqLn-YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_JYfDEfCdj8Tp72PBqA" keySequence="F3" command="_YqMK1IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_JofDEfCdj8Tp72PBqA" keySequence="F4" command="_YqMKX4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_J4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+ARROW_UP" command="_YqLpQYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_KIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_YqLokofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_KYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_UP" command="_YqLpq4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_KofDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YqMKzIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_K4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_LEFT" command="_YqLpK4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_LIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_YqLptIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_LYfDEfCdj8Tp72PBqA" keySequence="ALT+C" command="_YqLn-ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_LofDEfCdj8Tp72PBqA" keySequence="SHIFT+TAB" command="_YqLoP4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_L4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" bindingContext="_YqLANofDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_MIfDEfCdj8Tp72PBqA" keySequence="CTRL+TAB" command="_YqLpfofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_MYfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+P" command="_YqLpsofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_MofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+G" command="_YqLpk4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_M4fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+H" command="_YqLpN4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_NIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+R" command="_YqLoAIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_NYfDEfCdj8Tp72PBqA" keySequence="F3" command="_YqLpgIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_NofDEfCdj8Tp72PBqA" keySequence="F4" command="_YqLnqIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_N4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_UP" command="_YqLn6ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_OIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YqLn8ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_OYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" bindingContext="_YqLASIfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_OofDEfCdj8Tp72PBqA" keySequence="CTRL+CR" command="_YqLpMYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_O4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+C" command="_YqMKyIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_PIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+R" command="_YqMKOofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_PYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+U" command="_YqLot4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_PofDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+I" command="_YqMKMYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_P4fDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_UP" command="_YqLodIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_QIfDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_DOWN" command="_YqLpOofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_QYfDEfCdj8Tp72PBqA" keySequence="SHIFT+INSERT" command="_YqLoYofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_QofDEfCdj8Tp72PBqA" keySequence="INSERT" command="_YqLpt4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_Q4fDEfCdj8Tp72PBqA" keySequence="F4" command="_YqLoKYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_RIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_UP" command="_YqLpDIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_RYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+ARROW_DOWN" command="_YqMKNYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_RofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.contexts.window" bindingContext="_YqLAL4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_R4fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+T" command="_YqLoLIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_SIfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+L" command="_YqLoCIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_SYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q O" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_SofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_YqK_S4fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+B" command="_YqLohIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_TIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+R" command="_YqMK0ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_TYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q Q" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_TofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+S" command="_YqLoTofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_T4fDEfCdj8Tp72PBqA" keySequence="CTRL+3" command="_YqLo7IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_UIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+T" command="_YqLoeIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_UYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q S" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_UofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_YqK_U4fDEfCdj8Tp72PBqA" keySequence="CTRL+7" command="_YqMKZYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_VIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q V" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_VYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_YqK_VofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+G" command="_YqLoZ4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_V4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+W" command="_YqLpV4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_WIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q H" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_WYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_YqK_WofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+K" command="_YqLoaofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_W4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q K" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_XIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.mylyn.tasks.ui.views.tasks"/>
    </bindings>
    <bindings xmi:id="_YqK_XYfDEfCdj8Tp72PBqA" keySequence="CTRL+," command="_YqLn-4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_XofDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q L" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_X4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.pde.runtime.LogView"/>
    </bindings>
    <bindings xmi:id="_YqK_YIfDEfCdj8Tp72PBqA" keySequence="CTRL+." command="_YqMKhYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_YYfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+B" command="_YqLoa4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_YofDEfCdj8Tp72PBqA" keySequence="CTRL+#" command="_YqLoLofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_Y4fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+T" command="_YqLns4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_ZIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+E" command="_YqLojIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_ZYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q X" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_ZofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_YqK_Z4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q Y" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_aIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_YqK_aYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q Z" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_aofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_YqK_a4fDEfCdj8Tp72PBqA" keySequence="CTRL+P" command="_YqLpbYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_bIfDEfCdj8Tp72PBqA" keySequence="CTRL+Q" command="_YqLpf4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_bYfDEfCdj8Tp72PBqA" keySequence="CTRL+S" command="_YqMKMofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_bofDEfCdj8Tp72PBqA" keySequence="CTRL+W" command="_YqMKqYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_b4fDEfCdj8Tp72PBqA" keySequence="CTRL+H" command="_YqLpKYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_cIfDEfCdj8Tp72PBqA" keySequence="CTRL+K" command="_YqLohofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_cYfDEfCdj8Tp72PBqA" keySequence="CTRL+M" command="_YqLpIofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_cofDEfCdj8Tp72PBqA" keySequence="CTRL+N" command="_YqMKpIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_c4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+?" command="_YqLpE4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_YqK_dIfDEfCdj8Tp72PBqA" keySequence="CTRL+B" command="_YqLoAofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_dYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q B" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_dofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_YqK_d4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+Q C" command="_YqLoeofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_eIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_YqK_eYfDEfCdj8Tp72PBqA" keySequence="CTRL+E" command="_YqLpRofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_eofDEfCdj8Tp72PBqA" keySequence="CTRL+F" command="_YqLoVIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_e4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+W" command="_YqMKgofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_fIfDEfCdj8Tp72PBqA" keySequence="CTRL+8" command="_YqLpLIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_fYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+H" command="_YqLpM4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_fofDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+N" command="_YqLpU4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_f4fDEfCdj8Tp72PBqA" keySequence="CTRL+_" command="_YqLpGIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_gIfDEfCdj8Tp72PBqA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_YqK_gYfDEfCdj8Tp72PBqA" keySequence="CTRL+{" command="_YqLpGIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <parameters xmi:id="_YqK_gofDEfCdj8Tp72PBqA" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_YqK_g4fDEfCdj8Tp72PBqA" keySequence="SHIFT+F9" command="_YqLpbofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_hIfDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_LEFT" command="_YqLoM4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_hYfDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_RIGHT" command="_YqLpd4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_hofDEfCdj8Tp72PBqA" keySequence="SHIFT+F5" command="_YqMKeYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_h4fDEfCdj8Tp72PBqA" keySequence="ALT+F7" command="_YqLn9ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_iIfDEfCdj8Tp72PBqA" keySequence="F9" command="_YqLpnYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_iYfDEfCdj8Tp72PBqA" keySequence="F11" command="_YqMKVIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_iofDEfCdj8Tp72PBqA" keySequence="F12" command="_YqLpLofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_i4fDEfCdj8Tp72PBqA" keySequence="F2" command="_YqLoAIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_jIfDEfCdj8Tp72PBqA" keySequence="F5" command="_YqLplYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_jYfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F7" command="_YqMKWYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_jofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F8" command="_YqLpF4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_j4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F9" command="_YqMKUofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_kIfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+ARROW_LEFT" command="_YqLpf4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_kYfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+ARROW_RIGHT" command="_YqLod4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_kofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F4" command="_YqLpV4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_k4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+F6" command="_YqLoU4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_lIfDEfCdj8Tp72PBqA" keySequence="CTRL+F7" command="_YqLnu4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_lYfDEfCdj8Tp72PBqA" keySequence="CTRL+F8" command="_YqLo4ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_lofDEfCdj8Tp72PBqA" keySequence="CTRL+F9" command="_YqLok4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_l4fDEfCdj8Tp72PBqA" keySequence="CTRL+F11" command="_YqLpmofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_mIfDEfCdj8Tp72PBqA" keySequence="CTRL+F12" command="_YqLocIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_mYfDEfCdj8Tp72PBqA" keySequence="CTRL+F4" command="_YqMKqYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_mofDEfCdj8Tp72PBqA" keySequence="CTRL+F6" command="_YqLoXofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_m4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+F7" command="_YqLopofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_nIfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+ARROW_UP" command="_YqLoOIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_nYfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+ARROW_DOWN" command="_YqMKr4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_nofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+ARROW_RIGHT" command="_YqLoFofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_n4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_YqLoTYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_oIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_YqLpG4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_oYfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+SHIFT+F12" command="_YqMKa4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_oofDEfCdj8Tp72PBqA" keySequence="DEL" command="_YqLoZYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_o4fDEfCdj8Tp72PBqA" keySequence="ALT+?" command="_YqLpE4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_YqK_pIfDEfCdj8Tp72PBqA" keySequence="ALT+-" command="_YqLn2YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_pYfDEfCdj8Tp72PBqA" keySequence="ALT+CR" command="_YqLpAofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_pofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_YqLASYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_p4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+T" command="_YqLpEofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_qIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+H" command="_YqLoPYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_qYfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+G" command="_YqLnpIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_qofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+H" command="_YqLo5IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_q4fDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+I" command="_YqLn_4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_rIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+R" command="_YqLpO4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_rYfDEfCdj8Tp72PBqA" keySequence="CTRL+G" command="_YqMKlIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_rofDEfCdj8Tp72PBqA" keySequence="F3" command="_YqMK1IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_r4fDEfCdj8Tp72PBqA" keySequence="F4" command="_YqMKX4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_sIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_YqLAMYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_sYfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+V" command="_YqLprofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_sofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+C" command="_YqLo9IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_s4fDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_UP" command="_YqLnrYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_tIfDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_RIGHT" command="_YqMKTIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_tYfDEfCdj8Tp72PBqA" keySequence="SHIFT+INSERT" command="_YqLprofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_tofDEfCdj8Tp72PBqA" keySequence="CTRL+INSERT" command="_YqLo9IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_t4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" bindingContext="_YqLAN4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_uIfDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+M" command="_YqLn4ofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_uYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+C" command="_YqMKyIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_uofDEfCdj8Tp72PBqA" keySequence="CTRL+O" command="_YqMKdYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_u4fDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+R" command="_YqMKOofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_vIfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+S" command="_YqLpXofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_vYfDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+U" command="_YqLot4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_vofDEfCdj8Tp72PBqA" keySequence="ALT+SHIFT+I" command="_YqMKMYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_v4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_YqLAOIfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_wIfDEfCdj8Tp72PBqA" keySequence="CTRL+/" command="_YqLof4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_wYfDEfCdj8Tp72PBqA" keySequence="F3" command="_YqLoR4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_wofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" bindingContext="_YqLAO4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_w4fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+O" command="_YqLn7IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_xIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_YqLAQYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_xYfDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+M" command="_YqMKWofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_xofDEfCdj8Tp72PBqA" keySequence="ALT+CTRL+N" command="_YqMKbIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_x4fDEfCdj8Tp72PBqA" keySequence="CTRL+T" command="_YqLo94fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_yIfDEfCdj8Tp72PBqA" keySequence="CTRL+W" command="_YqLoJ4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_yYfDEfCdj8Tp72PBqA" keySequence="CTRL+N" command="_YqLoW4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_yofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.debugging" bindingContext="_YqLAQofDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_y4fDEfCdj8Tp72PBqA" keySequence="CTRL+R" command="_YqLnwYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_zIfDEfCdj8Tp72PBqA" keySequence="F7" command="_YqMKj4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_zYfDEfCdj8Tp72PBqA" keySequence="F8" command="_YqLoFIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_zofDEfCdj8Tp72PBqA" keySequence="F5" command="_YqLoFYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_z4fDEfCdj8Tp72PBqA" keySequence="F6" command="_YqMKRIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_0IfDEfCdj8Tp72PBqA" keySequence="CTRL+F2" command="_YqLpNIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_0YfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_YqLAQ4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_0ofDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+," command="_YqLpgofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_04fDEfCdj8Tp72PBqA" keySequence="CTRL+SHIFT+." command="_YqLpHYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_1IfDEfCdj8Tp72PBqA" keySequence="CTRL+G" command="_YqLpHofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_1YfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.DiffViewer" bindingContext="_YqLANIfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_1ofDEfCdj8Tp72PBqA" keySequence="CTRL+O" command="_YqMKgIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_14fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" bindingContext="_YqLAOofDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_2IfDEfCdj8Tp72PBqA" keySequence="CTRL+O" command="_YqLn7IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_2YfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_YqLASofDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_2ofDEfCdj8Tp72PBqA" keySequence="CTRL+C" command="_YqLo_IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_24fDEfCdj8Tp72PBqA" keySequence="CTRL+ARROW_LEFT" command="_YqLoRIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_3IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_YqLARofDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_3YfDEfCdj8Tp72PBqA" keySequence="CTRL+C" command="_YqLoYYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_3ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_YqLARIfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_34fDEfCdj8Tp72PBqA" keySequence="CTRL+G" command="_YqMKnofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_4IfDEfCdj8Tp72PBqA" keySequence="HOME" command="_YqLo8IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_4YfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.console" bindingContext="_YqLAPYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_4ofDEfCdj8Tp72PBqA" keySequence="CTRL+Z" command="_YqMKfIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_44fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_YqLARYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_5IfDEfCdj8Tp72PBqA" keySequence="SHIFT+F7" command="_YqLoQ4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_5YfDEfCdj8Tp72PBqA" keySequence="SHIFT+F8" command="_YqLpFofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_5ofDEfCdj8Tp72PBqA" keySequence="SHIFT+F5" command="_YqMKRYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_54fDEfCdj8Tp72PBqA" keySequence="SHIFT+F6" command="_YqLoBYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_6IfDEfCdj8Tp72PBqA" keySequence="CTRL+F5" command="_YqMKQYfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_6YfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_YqLATYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_6ofDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_LEFT" command="_YqLoqofDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_64fDEfCdj8Tp72PBqA" keySequence="ALT+ARROW_RIGHT" command="_YqLozIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_7IfDEfCdj8Tp72PBqA" keySequence="F3" command="_YqMK1IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_7YfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" bindingContext="_YqLAOYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_7ofDEfCdj8Tp72PBqA" keySequence="F1" command="_YqLnw4fDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_74fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" bindingContext="_YqLAS4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_8IfDEfCdj8Tp72PBqA" keySequence="F2" command="_YqLoaIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_8YfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.asmEditorScope" bindingContext="_YqLANYfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_8ofDEfCdj8Tp72PBqA" keySequence="F3" command="_YqMK1IfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_84fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView" bindingContext="_YqLAQIfDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_9IfDEfCdj8Tp72PBqA" keySequence="CTRL+INSERT" command="_YqLoTIfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqK_9YfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_YqLAR4fDEfCdj8Tp72PBqA">
    <bindings xmi:id="_YqK_9ofDEfCdj8Tp72PBqA" keySequence="ALT+Y" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_94fDEfCdj8Tp72PBqA" keySequence="ALT+A" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_-IfDEfCdj8Tp72PBqA" keySequence="ALT+B" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_-YfDEfCdj8Tp72PBqA" keySequence="ALT+C" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_-ofDEfCdj8Tp72PBqA" keySequence="ALT+D" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK_-4fDEfCdj8Tp72PBqA" keySequence="ALT+E" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK__IfDEfCdj8Tp72PBqA" keySequence="ALT+F" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK__YfDEfCdj8Tp72PBqA" keySequence="ALT+G" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK__ofDEfCdj8Tp72PBqA" keySequence="ALT+P" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqK__4fDEfCdj8Tp72PBqA" keySequence="ALT+R" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLAAIfDEfCdj8Tp72PBqA" keySequence="ALT+S" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLAAYfDEfCdj8Tp72PBqA" keySequence="ALT+T" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLAAofDEfCdj8Tp72PBqA" keySequence="ALT+V" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLAA4fDEfCdj8Tp72PBqA" keySequence="ALT+W" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLABIfDEfCdj8Tp72PBqA" keySequence="ALT+H" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLABYfDEfCdj8Tp72PBqA" keySequence="ALT+L" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
    <bindings xmi:id="_YqLABofDEfCdj8Tp72PBqA" keySequence="ALT+N" command="_YqLn0YfDEfCdj8Tp72PBqA">
      <tags>schemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_YqLAB4fDEfCdj8Tp72PBqA" bindingContext="_YqLAUIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLACIfDEfCdj8Tp72PBqA" bindingContext="_YqLAUYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLACYfDEfCdj8Tp72PBqA" bindingContext="_YqLAUofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLACofDEfCdj8Tp72PBqA" bindingContext="_YqLAU4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAC4fDEfCdj8Tp72PBqA" bindingContext="_YqLAVIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLADIfDEfCdj8Tp72PBqA" bindingContext="_YqLAVYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLADYfDEfCdj8Tp72PBqA" bindingContext="_YqLAVofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLADofDEfCdj8Tp72PBqA" bindingContext="_YqLAV4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAD4fDEfCdj8Tp72PBqA" bindingContext="_YqLAWIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAEIfDEfCdj8Tp72PBqA" bindingContext="_YqLAWYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAEYfDEfCdj8Tp72PBqA" bindingContext="_YqLAWofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAEofDEfCdj8Tp72PBqA" bindingContext="_YqLAW4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAE4fDEfCdj8Tp72PBqA" bindingContext="_YqLAXIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAFIfDEfCdj8Tp72PBqA" bindingContext="_YqLAXYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAFYfDEfCdj8Tp72PBqA" bindingContext="_YqLAXofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAFofDEfCdj8Tp72PBqA" bindingContext="_YqLAX4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAF4fDEfCdj8Tp72PBqA" bindingContext="_YqLAYIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAGIfDEfCdj8Tp72PBqA" bindingContext="_YqLAYYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAGYfDEfCdj8Tp72PBqA" bindingContext="_YqLAYofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAGofDEfCdj8Tp72PBqA" bindingContext="_YqLAY4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAG4fDEfCdj8Tp72PBqA" bindingContext="_YqLAZIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAHIfDEfCdj8Tp72PBqA" bindingContext="_YqLAZYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAHYfDEfCdj8Tp72PBqA" bindingContext="_YqLAZofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAHofDEfCdj8Tp72PBqA" bindingContext="_YqLAZ4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAH4fDEfCdj8Tp72PBqA" bindingContext="_YqLAaIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAIIfDEfCdj8Tp72PBqA" bindingContext="_YqLAaYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAIYfDEfCdj8Tp72PBqA" bindingContext="_YqLAaofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAIofDEfCdj8Tp72PBqA" bindingContext="_YqLAa4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAI4fDEfCdj8Tp72PBqA" bindingContext="_YqLAbIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAJIfDEfCdj8Tp72PBqA" bindingContext="_YqLAbYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAJYfDEfCdj8Tp72PBqA" bindingContext="_YqLAbofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAJofDEfCdj8Tp72PBqA" bindingContext="_YqLAb4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAJ4fDEfCdj8Tp72PBqA" bindingContext="_YqLAcIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAKIfDEfCdj8Tp72PBqA" bindingContext="_YqLAcYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAKYfDEfCdj8Tp72PBqA" bindingContext="_YqLAcofDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAKofDEfCdj8Tp72PBqA" bindingContext="_YqLAc4fDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLAK4fDEfCdj8Tp72PBqA" bindingContext="_YqLAdIfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLALIfDEfCdj8Tp72PBqA" bindingContext="_YqLAdYfDEfCdj8Tp72PBqA"/>
  <bindingTables xmi:id="_YqLALYfDEfCdj8Tp72PBqA" bindingContext="_YqLAdofDEfCdj8Tp72PBqA"/>
  <rootContext xmi:id="_YqLALofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_YqLAL4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_YqLAMIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_YqLAMYfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal Control in Focus" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_YqLAMofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_YqLAM4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_YqLANIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.DiffViewer" name="In Diff Viewer"/>
        <children xmi:id="_YqLANYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.asmEditorScope" name="Assembly Editor" description="Editor for Assembly Source Files"/>
        <children xmi:id="_YqLANofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.genericeditor.genericEditorContext" name="in Generic Code Editor" description="When editing in the Generic Code Editor"/>
        <children xmi:id="_YqLAN4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.editors.task" name="In Tasks Editor"/>
        <children xmi:id="_YqLAOIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_YqLAOYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.internal.wikitext.ui.editor.basicMarkupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context">
          <children xmi:id="_YqLAOofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.editor.markupSourceContext" name="WikiText Markup Source Context" description="WikiText markup editing context"/>
          <children xmi:id="_YqLAO4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.tasks.ui.markupSourceContext" name="Task Markup Editor Source Context"/>
        </children>
        <children xmi:id="_YqLAPIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_YqLAPYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_YqLAPofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" name="In Terminal View" description="Show modified keyboard shortcuts in context menu"/>
      <children xmi:id="_YqLAP4fDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_YqLAQIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_YqLAQYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_YqLAQofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_YqLAQ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_YqLARIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_YqLARYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_YqLARofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_YqLAR4fDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal Typing Connected" description="Override ALT+x menu access keys while typing into the Terminal"/>
      <children xmi:id="_YqLASIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" name="In Tasks View"/>
      <children xmi:id="_YqLASYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_YqLASofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View">
        <children xmi:id="_YqLAS4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesView.SingleRepository" name="In Git Repositories View"/>
      </children>
    </children>
    <children xmi:id="_YqLATIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_YqLATYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_YqLATofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_YqLAT4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_YqLAUIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_YqLAUYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_YqLAUofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_YqLAU4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_YqLAVIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_YqLAVYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_YqLAVofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_YqLAV4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_YqLAWIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_YqLAWYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_YqLAWofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_YqLAW4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_YqLAXIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_YqLAXYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_YqLAXofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_YqLAX4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_YqLAYIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_YqLAYYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_YqLAYofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_YqLAY4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_YqLAZIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.SearchActionSet" name="Auto::org.eclipse.egit.ui.SearchActionSet"/>
  <rootContext xmi:id="_YqLAZYfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset" name="Auto::org.eclipse.embedcdt.debug.gdbjtag.restart.ui.actionset"/>
  <rootContext xmi:id="_YqLAZofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.actionSet" name="Auto::org.eclipse.mylyn.context.ui.actionSet"/>
  <rootContext xmi:id="_YqLAZ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.navigation" name="Auto::org.eclipse.mylyn.tasks.ui.navigation"/>
  <rootContext xmi:id="_YqLAaIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.navigation.additions" name="Auto::org.eclipse.mylyn.tasks.ui.navigation.additions"/>
  <rootContext xmi:id="_YqLAaYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_YqLAaofDEfCdj8Tp72PBqA" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_YqLAa4fDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_YqLAbIfDEfCdj8Tp72PBqA" elementId="org.eclipse.text.quicksearch.actionSet" name="Auto::org.eclipse.text.quicksearch.actionSet"/>
  <rootContext xmi:id="_YqLAbYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_YqLAbofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_YqLAb4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_YqLAcIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_YqLAcYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_YqLAcofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_YqLAc4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_YqLAdIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_YqLAdYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_YqLAdofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <descriptors xmi:id="_YqLAd4fDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
    <tags>removeOnHide</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAeIfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.views.quickpanel" label="Quick Panel" iconURI="platform:/plugin/com.cypress.studio.app/icons/config_file_16.png" tooltip="" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.cypress.studio.app.views.quickpanel.CyQuickPanel"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.cypress.studio.app"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAeYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAeofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAe4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAfIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.debuggerConsoleView" label="Debugger Console" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/debugger_console_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.debuggerconsole.DebuggerConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAfYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser" label="Memory Browser" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui.memory.memorybrowser/icons/memorybrowser_view.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui.memory.memorybrowser"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAfofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAf4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAgIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.gdb.ui.debugsources.view" label="Debug Sources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/debugsources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.debugsources.DebugSourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAgYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAgofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Build Targets" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAg4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAhIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAhYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAhofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAh4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAiIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAiYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAiofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAi4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAjIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAjYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAjofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAj4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAkIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.launchView" label="Launch Configurations" iconURI="platform:/plugin/org.eclipse.debug.ui.launchview/icons/run_exc.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.debug.ui.launchview/org.eclipse.debug.ui.launchview.internal.view.LaunchViewImpl">
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAkYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAkofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAk4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAlIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
    <tags>NoRestore</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAlYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAlofDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.views.PeripheralsView" label="Peripherals" iconURI="platform:/plugin/org.eclipse.embedcdt.debug.gdbjtag.ui/icons/peripheral.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.debug.gdbjtag.ui.render.peripherals.PeripheralsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.debug.gdbjtag.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAl4fDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/org.eclipse.embedcdt.managedbuild.packs.ui/icons/pdficon_small.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.managedbuild.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAmIfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/hardware_chip.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAmYfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/board.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAmofDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/info_obj.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAm4fDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.packs.ui.views.PacksView" label="CMSIS Packs" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/packages.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAnIfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/org.eclipse.embedcdt.packs.ui/icons/outline_co.png" tooltip="" category="CMSIS Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.embedcdt.internal.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.embedcdt.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:CMSIS Packs</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAnYfDEfCdj8Tp72PBqA" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAnofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.callHierarchy.callHierarchyView" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/call_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.callhierarchy.CallHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAn4fDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.lsp4e/icons/full/dlcl16/type_hierarchy.gif" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.operations.typeHierarchy.TypeHierarchyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAoIfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.ui.languageServersView" label="Language Servers" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="Language Servers" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.lsp4e.ui.LanguageServersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.lsp4e"/>
    <tags>View</tags>
    <tags>categoryTag:Language Servers</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAoYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.commons.repositories.ui.navigator.Repositories" label="Team Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.commons.repositories.ui/icons/eview16/repositories.gif" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.commons.repositories.ui.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.commons.repositories.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAoofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.views.tasks" label="Task List" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/task-list.png" tooltip="" allowMultiple="true" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAo4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.views.repositories" label="Task Repositories" iconURI="platform:/plugin/org.eclipse.mylyn.tasks.ui/icons/eview16/repositories.png" tooltip="" category="Mylyn" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.mylyn.internal.tasks.ui.views.TaskRepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.mylyn.tasks.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Mylyn</tags>
  </descriptors>
  <descriptors xmi:id="_YqLApIfDEfCdj8Tp72PBqA" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_YqLApYfDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLApofDEfCdj8Tp72PBqA" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAp4fDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.png" tooltip="" allowMultiple="true" category="Version Control (Team)" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Version Control (Team)</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAqIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.TerminalsView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.TerminalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAqYfDEfCdj8Tp72PBqA" elementId="org.eclipse.tcf.te.ui.terminals.TerminalsView" label="Terminals (Old)" iconURI="platform:/plugin/org.eclipse.tm.terminal.view.ui/icons/eview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Other" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.terminal.view.ui.view.OldTerminalsViewHandler"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Other</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAqofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAq4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLArIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.png" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_YqLArYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLArofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAr4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAsIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAsYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAsofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAs4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAtIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAtYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAtofDEfCdj8Tp72PBqA" elementId="org.eclipse.pde.runtime.LogView" label="Error Log" iconURI="platform:/plugin/org.eclipse.ui.views.log/icons/eview16/error_log.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.log.LogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views.log"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_YqLAt4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.minimap.MinimapView" label="Minimap" iconURI="platform:/plugin/org.eclipse.ui.workbench.texteditor/icons/full/eview16/minimap.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.minimap.MinimapView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.workbench.texteditor"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_YqLnk4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_YqLnlIfDEfCdj8Tp72PBqA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_YqLnlYfDEfCdj8Tp72PBqA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_YqLnlofDEfCdj8Tp72PBqA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <commands xmi:id="_YqLnnofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnn4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnoIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnoYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLnoofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_YqLno4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Build Target" description="Create a new make build target for the selected container." category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnpIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnpYfDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_YqMLZYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnpofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository..." description="Adds an existing Git repository to the Git Repositories view" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnp4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.FetchGitLabMergeRequest" commandName="Fetch GitLab Merge Request" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnqIfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.openTypeHierarchy" commandName="Open Type Hierarchy" description="Open Type Hierarchy for the selected item" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnqYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearActiveTime" commandName="Clear Active Time" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnqofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id Information To Clipboard" description="Copies the build identification information to the clipboard." category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnq4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnrIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.task.attachContext" commandName="Attach Context" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnrYfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.maximize" commandName="Maximize Active View or Editor" category="_YqMLY4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnrofDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.CyUserGuideCommand" commandName="User Guide" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLnr4fDEfCdj8Tp72PBqA" elementId="docFilePath" name="com.cypress.studio.app.sdkhelp.docFilePath"/>
  </commands>
  <commands xmi:id="_YqLnsIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnsYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnsofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_YqMLYofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLns4fDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.connector.local.command.launch" commandName="Open Local Terminal on Selection" category="_YqMLWIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLntIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.stopMultiSelection" commandName="End multi-selection" description="Unselects all multi-selections returning to a single cursor " category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLntYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_YqMLXIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLntofDEfCdj8Tp72PBqA" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_YqLnt4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnuIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnuYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnuofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnu4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnvIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_YqMLR4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLnvYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_YqLnvofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnv4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnwIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnwYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnwofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Revert" commandName="Revert Commit" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnw4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.editor.showCheatSheetCommand" commandName="Show Markup Cheat Sheet" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnxIfDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_YqMLaofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnxYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnxofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.symbolinfile" commandName="Go to Symbol in File" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnx4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnyIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnyYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnyofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLny4fDEfCdj8Tp72PBqA" elementId="org.eclipse.tm4e.languageconfiguration.toggleLineCommentCommand" commandName="Toggle Line Comment" category="_YqMLRofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnzIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnzYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnzofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLnz4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.filediff.OpenWorkingTree" commandName="Open Working Tree Version" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn0IfDEfCdj8Tp72PBqA" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn0YfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_YqMLY4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn0ofDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.CyTrainingMaterialCommand" commandName="ModusToolbox Training Material" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLn04fDEfCdj8Tp72PBqA" elementId="webUrlPath" name="https://github.com/Infineon/training-modustoolbox"/>
  </commands>
  <commands xmi:id="_YqLn1IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn1YfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_YqMLYofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn1ofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.task.clearContext" commandName="Clear Context" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn14fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn2IfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn2YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn2ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn24fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" description="Opens selected commit(s) in Commit Viewer(s)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn3IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn3YfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_YqMLbYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn3ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_YqMLcYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn34fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.commit.UnifiedDiffCommand" commandName="Show Unified Diff" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn4IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn4YfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn4ofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.maximizePart" commandName="Maximize Part" description="Maximize Part" category="_YqMLSofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn44fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn5IfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn5YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn5ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn54fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.FetchGiteaPullRequest" commandName="Fetch Gitea Pull Request" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn6IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to Resource" description="Go to a particular resource in the active view" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn6YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn6ofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.selectionRange.up" commandName="Enclosing Element" description="Expand Selection To Enclosing Element" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn64fDEfCdj8Tp72PBqA" elementId="org.eclipse.remote.ui.command.openTerminal" commandName="Open Command Shell" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn7IfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.quickOutlineCommand" commandName="Quick Outline" description="Open a popup dialog with a quick outline of the current document" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn7YfDEfCdj8Tp72PBqA" elementId="AnsiConsole.command.enable_disable" commandName="Enable / Disable ANSI Support" description="Enable / disable ANSI Support" category="_YqMLVIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn7ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn74fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn8IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn8YfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.switchLeftAndRight" commandName="Swap Left and Right View" description="Switch the left and right sides in the compare editor" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn8ofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.selectionRange.down" commandName="Restore To Last Selection" description="Expand Selection To Restore To Last Selection" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn84fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.convertToDocbookCommand" commandName="Generate Docbook" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn9IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn9YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn9ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn94fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn-IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Toggle Comment" description="Toggle comment the selected lines" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn-YfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn-ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn-4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn_IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLn_YfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository..." description="Clones a Git repository and adds the clone to the Git Repositories view" category="_YqMLaYfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLn_ofDEfCdj8Tp72PBqA" elementId="repositoryUri" name="Repository URI"/>
  </commands>
  <commands xmi:id="_YqLn_4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoAIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoAYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoAofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoA4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoBIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoBYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_YqMLbofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoBofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoB4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Git Repository..." description="Creates a new Git repository and adds it to the Git Repositories view" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoCIfDEfCdj8Tp72PBqA" elementId="org.eclipse.text.quicksearch.commands.quicksearchCommand" commandName="Quick Search" category="_YqMLWofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoCYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoCofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm4e.languageconfiguration.removeBlockCommentCommand" commandName="Remove Block Comment" category="_YqMLRofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoC4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_YqMLTYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoDIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoDYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoDofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoD4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoEIfDEfCdj8Tp72PBqA" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoEYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoEofDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_YqMLa4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoE4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_YqMLcofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoFIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoFYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoFofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.open.context.dialog" commandName="Show Context Quick View" description="Show Context Quick View" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoF4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoGIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoGYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CompareWithRef" commandName="Compare with Branch, Tag or Reference..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoGofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.attachment.retrieveContext" commandName="Retrieve Context Attachment" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoG4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoHIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoHYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoHofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.memory.memorybrowser.jumpToMemory" commandName="Jump to Memory" description="Open memory view and add memory monitor for address" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoH4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.convertToMarkupCommand" commandName="Generate Markup" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLoIIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.targetLanguage" name="TargetLanguage" optional="false"/>
  </commands>
  <commands xmi:id="_YqLoIYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.RefreshRepositoryTasks" commandName="Synchronize Changed" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoIofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoI4fDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowRepositoryCatalog" commandName="Show Repository Catalog" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLoJIfDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.RepositoryParameter" name="P2 Repository URI"/>
  </commands>
  <commands xmi:id="_YqLoJYfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.CyReleaseNotesCommand" commandName="Release Notes" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLoJofDEfCdj8Tp72PBqA" elementId="docFilePath" name="com.cypress.studio.app.sdkhelp.docFilePath"/>
  </commands>
  <commands xmi:id="_YqLoJ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoKIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoKYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.showToolTip" commandName="Show Tooltip Description" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoKofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.wsselection.command" commandName="Manage Working Sets" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoK4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.task.copyContext" commandName="Copy Context" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoLIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.command.launchToolbar" commandName="Open Local Terminal on Selection" category="_YqMLWIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoLYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoLofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoL4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoMIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoMYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoMofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoM4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoNIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoNYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoNofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoN4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoOIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.interest.increment" commandName="Make Landmark" description="Make Landmark" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoOYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoOofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoO4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoPIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewShowInSystemExplorer" commandName="Show In System Explorer" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoPYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoPofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoP4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoQIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoQYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoQofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesCreateGroup" commandName="Create a Repository Group" description="Create a repository group for structuring repositories in the Git Repositories view" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoQ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_YqMLbofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoRIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewCollapseWorkingTree" commandName="Collapse Working Tree" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoRYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoRofDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.packs.ui.commands.updateCommand" commandName="Refresh" category="_YqMLXofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoR4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_YqMLZIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoSIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoSYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoSofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoS4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_YqMLaofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoTIfDEfCdj8Tp72PBqA" elementId="AnsiConsole.command.copy_without_escapes" commandName="Copy Text Without ANSI Escapes" description="Copy the console content to clipboard, removing the escape sequences" category="_YqMLVIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoTYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoTofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoT4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoUIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoUYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoUofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoU4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoVIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoVYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoVofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoV4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoWIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLoWYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_YqLoWofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoW4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoXIfDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoXYfDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_YqMLRIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoXofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoX4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoYIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.markers.copyMarkerResourceQualifiedName" commandName="Copy Resource Qualified Name To Clipboard" description="Copies markers resource qualified name to the clipboard" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoYYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy Commit Id" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoYofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.new.subtask" commandName="New Subtask" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoY4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoZIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoZYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoZofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoZ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_YqMLZYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoaIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoaYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoaofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoa4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLobIfDEfCdj8Tp72PBqA" elementId="org.eclipse.launchbar.ui.command.buildActive" commandName="Build Active Launch Configuration" category="_YqMLZofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLobYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLobofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_YqMLbofDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLob4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.commands.radioStateParameter" name="TraceMethod" optional="false"/>
  </commands>
  <commands xmi:id="_YqLocIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.openTask" commandName="Open Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLocYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLocofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoc4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLodIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.goToPreviousUnread" commandName="Go To Previous Unread Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLodYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLodofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLod4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.gotoNextEditPosition" commandName="Next Edit Location" description="Next edit location" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoeIfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.symbolinworkspace" commandName="Go to Symbol in Workspace" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoeYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoeofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_YqMLRYfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLoe4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_YqLofIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_YqLofYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_YqLofofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLof4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.toggle.comment" commandName="Toggle Comment" description="Comment/uncomment selected lines with # style comments" category="_YqMLZIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLogIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLogYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLogofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_YqMLUofDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLog4fDEfCdj8Tp72PBqA" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_YqLohIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLohYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLohofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoh4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoiIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoiYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_YqMLV4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLoiofDEfCdj8Tp72PBqA" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_YqLoi4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLojIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLojYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Context Help" description="Open the contextual help" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLojofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoj4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toMultiSelection" commandName="To multi-selection" description="Turn current selection into multiple text selections" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLokIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.addTaskRepository" commandName="Add Task Repository..." category="_YqMLUYfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLokYfDEfCdj8Tp72PBqA" elementId="connectorKind" name="Repository Type"/>
  </commands>
  <commands xmi:id="_YqLokofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLok4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.activateTask" commandName="Activate Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLolIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLolYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLolofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_YqMLX4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLol4fDEfCdj8Tp72PBqA" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_YqLomIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLomYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.viewSource.command" commandName="View Unformatted Text" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLomofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLom4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLonIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLonYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.multiCaretDown" commandName="Multi caret down" description="Add a new caret/multi selection below the current line, or remove the first caret/multi selection " category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLonofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLon4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLooIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLooYfDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLooofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoo4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLopIfDEfCdj8Tp72PBqA" elementId="org.eclipse.launchbar.ui.command.launchActive" commandName="Launch Active Launch Configuration" category="_YqMLZofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLopYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLopofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLop4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoqIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoqYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Tree" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoqofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoq4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" description="Check out, rename, create, or delete a branch in a git repository" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLorIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLorYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLorofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLor4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.PullWithOptions" commandName="Pull..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLosIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLosYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLosofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLos4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.addAllMatchesToMultiSelection" commandName="Add all matches to multi-selection" description="Looks for all regions matching the current selection or identifier and adds them to a multi-selection " category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLotIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLotYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLotofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_YqMLYofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLot4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskUnread" commandName="Mark Task Unread" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLouIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_YqMLTYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLouYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_YqMLUIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLouofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLou4fDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_YqMLZYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLovIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLovYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLovofDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_YqMLZYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLov4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLowIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLowYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLowofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.command.launch" commandName="Open Terminal on Selection" category="_YqMLWIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLow4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoxIfDEfCdj8Tp72PBqA" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoxYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoxofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLox4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ReplaceWithTheirs" commandName="Replace Conflicting Files with Their Revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoyIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.filediff.CheckoutNew" commandName="Check Out This Version" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoyYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.toggleShowKeys" commandName="Toggle Show Key Bindings" description="Shows key binding when command is invoked" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoyofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoy4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.SynchronizeAll" commandName="Synchronize Changed" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLozIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLozYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ReplaceWithPrevious" commandName="Replace with Previous Revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLozofDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.CyModusShellAutoswitchCommand" commandName="Track Current Project" category="_YqMLU4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLoz4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo0IfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.context.ui.editor.folding.auto" commandName="Toggle Active Folding" description="Toggle Active Folding" category="_YqMLVYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo0YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo0ofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo04fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_YqMLX4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLo1IfDEfCdj8Tp72PBqA" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_YqLo1YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo1ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo14fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo2IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo2YfDEfCdj8Tp72PBqA" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo2ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo24fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_YqMLTYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo3IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.InstallLfsLocal" commandName="Enable LFS locally" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo3YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo3ofDEfCdj8Tp72PBqA" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo34fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CherryPick" commandName="Cherry Pick" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo4IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.FetchGitHubPR" commandName="Fetch GitHub Pull Request" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo4YfDEfCdj8Tp72PBqA" elementId="org.eclipse.launchbar.ui.command.configureActiveLaunch" commandName="Edit Active Launch Configuration" category="_YqMLZofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo4ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo44fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.UpdateRepositoryConfiguration" commandName="Update Repository Configuration" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo5IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo5YfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo5ofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.console.clear" commandName="Clear Console" description="Clear Console" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo54fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo6IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open this Version" category="_YqMLaYfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLo6YfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_YqLo6ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo64fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo7IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.quickAccess" commandName="Find Actions" description="Quickly access UI elements" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo7YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo7ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.override.methods" commandName="Override Methods..." description="Generates override methods for a selected class" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo74fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.attachment.open" commandName="Open Attachment" category="_YqMLSofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo8IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo8YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo8ofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo84fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo9IfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_YqMLY4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo9YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo9ofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo94fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo-IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo-YfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo-ofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.formatfile" commandName="Format" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo-4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo_IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLo_YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_YqMLa4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLo_ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource.newName.parameter.key" name="Selected resource's new name."/>
  </commands>
  <commands xmi:id="_YqLo_4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpAIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpAYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.previousTask" commandName="Previous Task Command" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpAofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpA4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpBIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpBYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpBofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_YqMLXIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpB4fDEfCdj8Tp72PBqA" elementId="url" name="URL"/>
    <parameters xmi:id="_YqLpCIfDEfCdj8Tp72PBqA" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_YqLpCYfDEfCdj8Tp72PBqA" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_YqLpCofDEfCdj8Tp72PBqA" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_YqLpC4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpDIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToPreviousUnread" commandName="Mark Task Read and Go To Previous Unread Task" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpDYfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.LaunchConfiguratorId" commandName="ModusToolbox" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpDofDEfCdj8Tp72PBqA" elementId="configuratorId" name="com.cypress.studio.app.commands.configuratorId"/>
    <parameters xmi:id="_YqLpD4fDEfCdj8Tp72PBqA" elementId="configuratorFile" name="com.cypress.studio.app.commands.configuratorFile"/>
  </commands>
  <commands xmi:id="_YqLpEIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpEYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpEofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpE4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpFIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpFYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpFofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_YqMLbofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpF4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpGIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_YqMLXIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpGYfDEfCdj8Tp72PBqA" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_YqLpGofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpG4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpHIfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpHYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpHofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpH4fDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.togglelinkwitheditor" commandName="Toggle Link with Editor" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpIIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateSelectedTask" commandName="Deactivate Selected Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpIYfDEfCdj8Tp72PBqA" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpIofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpI4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpJIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.lockToolBar" commandName="Toggle Lock Toolbars" description="Toggle the Lock on the Toolbars" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpJYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpJofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.newEditor" commandName="Clone Editor" description="Open another editor on the active editor's input" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpJ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpKIfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.format" commandName="Format" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpKYfDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_YqMLZYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpKofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpK4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpLIfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.swcompGui" commandName="ModusToolbox Library Manager" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpLYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpLofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpL4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpMIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpMYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.openSelectedTask" commandName="Open Selected Task" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpMofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Toggle &quot;Link with Editor and Selection&quot; (Git Repositories View)" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpM4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.toggle.focus.active.view" commandName="Focus on Active Task" description="Toggle the focus on active task for the active view" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpNIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpNYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpNofDEfCdj8Tp72PBqA" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpN4fDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.openCallHierarchy" commandName="Open Call Hierarchy" description="Open Call Hierarchy for the selected item" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpOIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpOYfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpOofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.goToNextUnread" commandName="Go To Next Unread Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpO4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpPIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm4e.languageconfiguration.addBlockCommentCommand" commandName="Add Block Comment" category="_YqMLRofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpPYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_YqMLcofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpPofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpP4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpQIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpQYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpQofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="Interactive Rebase" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpQ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_YqMLaofDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpRIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_YqLpRYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_YqLpRofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpR4fDEfCdj8Tp72PBqA" elementId="AnsiConsole.command.copy_with_escapes" commandName="Copy Text With ANSI Escapes" description="Copy the console content to clipboard, including the escape sequences" category="_YqMLVIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpSIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpSYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpSofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpS4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpTIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpTYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpTofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpT4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpUIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpUYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpUofDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpU4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpVIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpVYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpVofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpV4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpWIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.filediff.OpenPrevious" commandName="Open Previous Version" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpWYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpWofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpW4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpXIfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.SDKHelpDocCommandId" commandName="SDK Doc Help" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpXYfDEfCdj8Tp72PBqA" elementId="docFilePath" name="com.cypress.studio.app.sdkhelp.docFilePath"/>
  </commands>
  <commands xmi:id="_YqLpXofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.submitTask" commandName="Submit Task" description="Submits the currently open task" category="_YqMLSofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpX4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpYIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpYYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpYofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpY4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpZIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpZYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpZofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.internal.merge.ToggleCurrentChangesCommand" commandName="Ignore Changes from Ancestor to Current Version" description="Toggle ignoring changes only between the ancestor and the current version in a three-way merge comparison" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpZ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_YqMLV4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpaIfDEfCdj8Tp72PBqA" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_YqLpaYfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_YqMLTofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpaofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpa4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpbIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_YqMLa4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpbYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpbofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Build Target Build" description="Invoke a make target build for the selected container." category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpb4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionDown" commandName="Multi selection down relative to anchor selection  " description="Search next matching region and add it to the current selection, or remove first element from current multi-selection " category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpcIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpcYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpcofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpc4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.task.clearOutgoing" commandName="Clear Outgoing Changes" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpdIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpdYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpdofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpd4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpeIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpeYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.align.const" commandName="Align const qualifiers" description="Moves const qualifiers to the left or right of the type depending on the workspace preferences" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpeofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpe4fDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_YqMLZYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpfIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpfYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpfofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.lsp.clangd.editor.commands.command.switchSourceHeader" commandName="Toggle Source/Header" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpf4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Previous Edit Location" description="Previous edit location" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpgIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpgYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.convertToEclipseHelpCommand" commandName="Generate Eclipse Help (*.html and *-toc.xml)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpgofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpg4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLphIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLphYfDEfCdj8Tp72PBqA" elementId="org.eclipse.launchbar.ui.command.stop" commandName="Stop" category="_YqMLZofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLphofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.configureFilters" commandName="Filters..." description="Configure the filters to apply to the markers view" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLph4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpiIfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.toggleSortOutline" commandName="Sort" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpiYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_YqMLaIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpiofDEfCdj8Tp72PBqA" elementId="title" name="Title"/>
    <parameters xmi:id="_YqLpi4fDEfCdj8Tp72PBqA" elementId="message" name="Message"/>
    <parameters xmi:id="_YqLpjIfDEfCdj8Tp72PBqA" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_YqLpjYfDEfCdj8Tp72PBqA" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_YqLpjofDEfCdj8Tp72PBqA" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_YqLpj4fDEfCdj8Tp72PBqA" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_YqLpkIfDEfCdj8Tp72PBqA" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_YqLpkYfDEfCdj8Tp72PBqA" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_YqLpkofDEfCdj8Tp72PBqA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_YqLpk4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.genericeditor.findReferences" commandName="Find References" description="Find other code items referencing the current selected item." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLplIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLplYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLplofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ReplaceWithOurs" commandName="Replace Conflicting Files with Our Revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpl4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpmIfDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.packs.ui.commands.showPerspectiveCommand" commandName="Switch to CMSIS Packs Perspective" category="_YqMLXofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpmYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpmofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpm4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpnIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpnYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpnofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.gdb.ui.command.osview.connect" commandName="Connect" description="Connect to selected processes" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpn4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.TerminateAll" commandName="Terminate/Disconnect All" description="Terminate/Disconnect All" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpoIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpoYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpoofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpo4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_YqMLcofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLppIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_YqMLYofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLppYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.disconnected" commandName="Disconnected" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLppofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpp4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpqIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpqYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpqofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpq4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLprIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLprYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLprofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_YqMLY4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpr4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpsIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Tag" commandName="Create Tag..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpsYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpsofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.genericeditor.gotoMatchingBracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLps4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLptIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLptYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_YqMLUofDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLptofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_YqLpt4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.new.local.task" commandName="New Local Task" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpuIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpuYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpuofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqLpu4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_YqLpvIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_YqLpvYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpvofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.genericeditor.togglehighlight" commandName="Toggle Highlight" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpv4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqLpwIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKMIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKMYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskIncomplete" commandName="Mark Task Incomplete" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKMofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKM4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_YqMLcofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKNIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKNYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskReadGoToNextUnread" commandName="Mark Task Read and Go To Next Unread Task" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKNofDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_YqMLRIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKN4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_YqMLcYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKOIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKOYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKOofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskRead" commandName="Mark Task Read" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKO4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKPIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKPYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKPofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKP4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKQIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKQYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKQofDEfCdj8Tp72PBqA" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKQ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_YqMLRIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKRIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKRYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_YqMLbofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKRofDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKR4fDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.ApiHelpCommandId" commandName="API Reference" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKSIfDEfCdj8Tp72PBqA" elementId="docFilePath" name="com.cypress.studio.app.sdkhelp.parameter1"/>
  </commands>
  <commands xmi:id="_YqMKSYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKSofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKS4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKTIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.quickaccess" commandName="Quick Access" category="_YqMLY4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKTYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_YqMLV4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKTofDEfCdj8Tp72PBqA" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_YqMKT4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKUIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.CompareWithCommit" commandName="Compare with Commit..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKUYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKUofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.deactivateAllTasks" commandName="Deactivate Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKU4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKVIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKVYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.convertToHtmlCommand" commandName="Generate HTML" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKVofDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.KitHelpCommandId" commandName="Kit Help" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKV4fDEfCdj8Tp72PBqA" elementId="docFilePath" name="com.cypress.studio.app.sdkhelp.parameter1"/>
  </commands>
  <commands xmi:id="_YqMKWIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKWYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKWofDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKW4fDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.CyESGCommand" commandName="Survival Guide" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKXIfDEfCdj8Tp72PBqA" elementId="webUrlPath" name="com.cypress.studio.app.sdkhelp.webUrlPath"/>
  </commands>
  <commands xmi:id="_YqMKXYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKXofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKX4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKYIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.markers.copyDescription" commandName="Copy Description To Clipboard" description="Copies markers description field to the clipboard" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKYYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_YqMLTYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKYofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKY4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKZIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKZYfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.newProjectCommand" commandName="ModusToolbox&#x2122; Application" category="_YqMLW4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKZofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.command.disconnect" commandName="Disconnect Terminal" category="_YqMLWIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKZ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Revision Information" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKaIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKaYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKaofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_YqMLbYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKa4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.openRemoteTask" commandName="Open Remote Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKbIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKbYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.CompareWithEachOther" commandName="Compare with Each Other" description="Compare two files selected in the Compare Editor with each other." category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKbofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.task.retrieveContext" commandName="Retrieve Context" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKb4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKcIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKcYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.menu.manage.configs.command" commandName="Manage Build Configurations" category="_YqMLYIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKcofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKc4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.togglestatusbar" commandName="Toggle Statusbar" description="Toggle the visibility of the bottom status bar" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKdIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.selectMultiSelectionUp" commandName="Multi selection up relative to anchor selection" description="Search next matching region above and add it to the current selection, or remove last element from current multi-selection " category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKdYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.task.ui.editor.QuickOutline" commandName="Quick Outline" description="Show the quick outline for the editor input" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKdofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKd4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKeIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.multiCaretUp" commandName="Multi caret up" description="Add a new caret/multi selection above the current line, or remove the last caret/multi selection " category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKeYfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKeofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKe4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKfIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKfYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.commons.ui.command.AddRepository" commandName="Add Repository" category="_YqMLbIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKfofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKf4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_YqMLcofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKgIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.commit.DiffEditorQuickOutlineCommand" commandName="Quick Outline" description="Show the quick outline for a unified diff" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKgYfDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.typeHierarchy" commandName="Quick Type Hierarchy" description="Open Quick Call Hierarchy for the selected item" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKgofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKg4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKhIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKhYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKhofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKh4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_YqMLXIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKiIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_YqMKiYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_YqMLX4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKiofDEfCdj8Tp72PBqA" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_YqMKi4fDEfCdj8Tp72PBqA" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_YqMKjIfDEfCdj8Tp72PBqA" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_YqMKjYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.focus.view" commandName="Focus View" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKjofDEfCdj8Tp72PBqA" elementId="viewId" name="View ID to Focus" optional="false"/>
  </commands>
  <commands xmi:id="_YqMKj4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKkIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_YqMLXIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKkYfDEfCdj8Tp72PBqA" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_YqMKkofDEfCdj8Tp72PBqA" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_YqMKk4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKlIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKlYfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.SDKHelpCommandId" commandName="SDK URL Help" category="_YqMLcIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKlofDEfCdj8Tp72PBqA" elementId="docFilePath" name="com.cypress.studio.app.sdkhelp.parameter1"/>
  </commands>
  <commands xmi:id="_YqMKl4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_YqMLX4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKmIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKmYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_YqMLbYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKmofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.activateSelectedTask" commandName="Activate Selected Task" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKm4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKnIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKnYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_YqMLXYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKnofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKn4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKoIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_YqMLYYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKoYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKoofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.command.newview" commandName="New Terminal View" category="_YqMLWIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKo4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKpIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_YqMLV4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKpYfDEfCdj8Tp72PBqA" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_YqMKpofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKp4fDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.showkindinoutline" commandName="Show Kind in Outline" category="_YqMLVofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKqIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKqYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_YqMLV4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKqofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKq4fDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with Each Other" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKrIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKrYfDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKrofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKr4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands.interest.decrement" commandName="Make Less Interesting" description="Make Less Interesting" category="_YqMLT4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKsIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKsYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Check Out" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKsofDEfCdj8Tp72PBqA" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKs4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKtIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKtYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_YqMKtofDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.ShowBundleCatalog" commandName="Show Bundle Catalog" category="_YqMLb4fDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKt4fDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.DirectoryParameter" name="Directory URL"/>
    <parameters xmi:id="_YqMKuIfDEfCdj8Tp72PBqA" elementId="org.eclipse.equinox.p2.ui.discovery.commands.TagsParameter" name="Tags"/>
  </commands>
  <commands xmi:id="_YqMKuYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_YqMLUofDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKuofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_YqMLUofDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKu4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_YqMKvIfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKvYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_YqMLaIfDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMKvofDEfCdj8Tp72PBqA" elementId="title" name="Title"/>
    <parameters xmi:id="_YqMKv4fDEfCdj8Tp72PBqA" elementId="message" name="Message"/>
    <parameters xmi:id="_YqMKwIfDEfCdj8Tp72PBqA" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_YqMKwYfDEfCdj8Tp72PBqA" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_YqMKwofDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKw4fDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKxIfDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKxYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKxofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_YqMLcYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKx4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKyIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.command.markTaskComplete" commandName="Mark Task Complete" category="_YqMLUYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKyYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKyofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKy4fDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKzIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_YqMLR4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKzYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_YqMLZ4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKzofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_YqMLTIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMKz4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_YqMLXIfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK0IfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_YqMLaYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK0YfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.text.c.copy.qualified.name" commandName="Copy Qualified Name" description="Copy a fully qualified name to the system clipboard" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK0ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_YqMLUofDEfCdj8Tp72PBqA">
    <parameters xmi:id="_YqMK04fDEfCdj8Tp72PBqA" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_YqMK1IfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_YqMLSYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK1YfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_YqMLWYfDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK1ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK14fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK2IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK2YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK2ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK24fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK3IfDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK3YfDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK3ofDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK34fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK4IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK4YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK4ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK44fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK5IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK5YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK5ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK54fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK6IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK6YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK6ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK64fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK7IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK7YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK7ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK74fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.egit.ui.SearchActionSet/org.eclipse.egit.ui.actions.OpenCommitSearchPage" commandName="Git..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK8IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.navigation.additions/org.eclipse.mylyn.tasks.ui.navigate.task.history" commandName="Activate Previous Task" description="Activate Previous Task" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK8YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK8ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK84fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK9IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK9YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK9ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK94fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK-IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.lsp.CEditor.BreakpointRulerActions/org.eclipse.cdt.lsp.CEditor.RulerToggleBreakpointAction" commandName="unusedlabel" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK-YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK-ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK-4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK_IfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK_YfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK_ofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMK_4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLAIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLAYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLAofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLA4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLBIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLBYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLBofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLB4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLCIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLCYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.floatingpoint.preferenceaction" commandName="Floating Point Rendering Preferences ..." description="Floating Point Rendering Preferences ..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLCofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.pinDebugContext" commandName="Pin to Debug Context" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLC4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memoryBrowser.toolbar/org.eclipse.pinclone.memoryBrowser.clone" commandName="Open New View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLDIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.clearExpressionList/org.eclipse.cdt.debug.ui.memory.memorybrowser.ClearExpressionListActionID" commandName="Clear Expressions" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLDYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLDofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.MemoryView.findReplace/org.eclipse.cdt.debug.ui.memory.search.FindAction" commandName="Find/Replace..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLD4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.findNext/org.eclipse.cdt.debug.ui.memory.search.FindNextAction" commandName="Find Next" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLEIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.traditional.preferenceaction" commandName="Traditional Rendering Preferences..." description="Traditional Rendering Preferences..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLEYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLEofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction" commandName="Import" description="Import" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLE4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ExportMemoryAction" commandName="Export" description="Export" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLFIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.memory.memorybrowser.MemoryBrowser.toolbar/org.eclipse.cdt.debug.ui.memory.transport.actions.ImportMemoryAction2" commandName="Import" description="Import" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLFYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh/org.eclipse.cdt.dsf.gdb.ui.debugsources.view.refresh" commandName="Refresh" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLFofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.breakpoints.update.Refresh/org.eclipse.cdt.dsf.debug.ui.breakpoints.viewmodel.update.actions.refresh" commandName="Refresh" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLF4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLGIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLGYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLGofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLG4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLHIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLHYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLHofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLH4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.collapseAll" commandName="Collapse All" description="Collapse All" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLIIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLIYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLIofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLI4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLJIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLJYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.sortByAction" commandName="Sort By" description="Sort By" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLJofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLJ4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLKIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLKYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLKofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLK4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLLIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLLYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLLofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLL4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLMIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLMYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.context.ui.outline.contribution/org.eclipse.mylyn.context.ui.contentOutline.focus" commandName="Focus on Active Task" description="Focus on Active Task (Alt+click to reveal filtered elements)" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLMofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.synchronize.changed" commandName="Synchronize Changed" description="Synchronize Changed" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLM4fDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.tasks.restore" commandName="Restore Tasks from History..." category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLNIfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.tasks.ui.open.repositories.view" commandName="Show Task Repositories View" description="Show Task Repositories View" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLNYfDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.doc.legend.show.action" commandName="Show UI Legend" description="Show Tasks UI Legend" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <commands xmi:id="_YqMLNofDEfCdj8Tp72PBqA" elementId="AUTOGEN:::org.eclipse.mylyn.tasks.ui.actions.view/org.eclipse.mylyn.context.ui.actions.tasklist.focus" commandName="Focus on Workweek" description="Focus on Workweek" category="_YqMLb4fDEfCdj8Tp72PBqA"/>
  <addons xmi:id="_YqMLN4fDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_YqMLOIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_YqMLOYfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_YqMLOofDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_YqMLO4fDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_YqMLPIfDEfCdj8Tp72PBqA" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_YqMLPYfDEfCdj8Tp72PBqA" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_YqMLPofDEfCdj8Tp72PBqA" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_YqMLP4fDEfCdj8Tp72PBqA" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon"/>
  <addons xmi:id="_YqMLQIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_YqMLQYfDEfCdj8Tp72PBqA" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_YqMLQofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_YqMLQ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <categories xmi:id="_YqMLRIfDEfCdj8Tp72PBqA" elementId="org.eclipse.team.ui.category.team" name="Version control (Team)" description="Actions that apply when working with a version control system"/>
  <categories xmi:id="_YqMLRYfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_YqMLRofDEfCdj8Tp72PBqA" elementId="org.eclipse.tm4e.languageconfiguration.category" name="TM4E Language Configuration"/>
  <categories xmi:id="_YqMLR4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_YqMLSIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.ui.editor.category" name="WikiText Markup Editing Commands" description="commands for editing lightweight markup"/>
  <categories xmi:id="_YqMLSYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_YqMLSofDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.category.editor" name="Task Editor"/>
  <categories xmi:id="_YqMLS4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_YqMLTIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_YqMLTYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_YqMLTofDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.debug.gdbjtag.restart.ui.category" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_YqMLT4fDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.context.ui.commands" name="Focused UI" description="Task-Focused Interface"/>
  <categories xmi:id="_YqMLUIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_YqMLUYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.tasks.ui.commands" name="Task Repositories"/>
  <categories xmi:id="_YqMLUofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_YqMLU4fDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.category.terminal" name="Terminal"/>
  <categories xmi:id="_YqMLVIfDEfCdj8Tp72PBqA" elementId="AnsiConsole.command.categoryid" name="ANSI Support Commands"/>
  <categories xmi:id="_YqMLVYfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.wikitext.context.ui.commands" name="Mylyn WikiText" description="Commands used for Mylyn WikiText"/>
  <categories xmi:id="_YqMLVofDEfCdj8Tp72PBqA" elementId="org.eclipse.lsp4e.category" name="Language Servers"/>
  <categories xmi:id="_YqMLV4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_YqMLWIfDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.view.ui.commands.category" name="Terminal Commands"/>
  <categories xmi:id="_YqMLWYfDEfCdj8Tp72PBqA" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_YqMLWofDEfCdj8Tp72PBqA" elementId="org.eclipse.text.quicksearch.commands.category" name="Quick Search"/>
  <categories xmi:id="_YqMLW4fDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.category2" name="File"/>
  <categories xmi:id="_YqMLXIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_YqMLXYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_YqMLXofDEfCdj8Tp72PBqA" elementId="org.eclipse.embedcdt.packs.ui.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_YqMLX4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_YqMLYIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_YqMLYYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_YqMLYofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_YqMLY4fDEfCdj8Tp72PBqA" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_YqMLZIfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_YqMLZYfDEfCdj8Tp72PBqA" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_YqMLZofDEfCdj8Tp72PBqA" elementId="org.eclipse.launchbar.ui.category.launchBar" name="Launch Bar"/>
  <categories xmi:id="_YqMLZ4fDEfCdj8Tp72PBqA" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_YqMLaIfDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_YqMLaYfDEfCdj8Tp72PBqA" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_YqMLaofDEfCdj8Tp72PBqA" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_YqMLa4fDEfCdj8Tp72PBqA" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_YqMLbIfDEfCdj8Tp72PBqA" elementId="org.eclipse.mylyn.commons.repositories.ui.category.Team" name="Team"/>
  <categories xmi:id="_YqMLbYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_YqMLbofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_YqMLb4fDEfCdj8Tp72PBqA" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_YqMLcIfDEfCdj8Tp72PBqA" elementId="com.cypress.studio.app.commands.category.help" name="Help"/>
  <categories xmi:id="_YqMLcYfDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_YqMLcofDEfCdj8Tp72PBqA" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
