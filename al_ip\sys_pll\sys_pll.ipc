<?xml version='1.0' encoding='utf-8'?>
<PLLConfig version="1.0">
    <GeneralConfig>
        <Type>PLL</Type>
        <Device>PH1A90SBG484</Device>
        <create_VHDL>true</create_VHDL>
    </GeneralConfig>
    <Page1>
        <speed_grade>Any</speed_grade>
        <input_frequency>25.0000000000000000MHz</input_frequency>
        <feedback_mode>Normal</feedback_mode>
        <clk_num>CLKC0</clk_num>
        <power_down_up>DISABLE</power_down_up>
        <ssc_enable>DISABLE</ssc_enable>
        <use_fre_modulation>DISABLE</use_fre_modulation>
        <dynamicCPhase>DISABLE</dynamicCPhase>
        <dynamicFPhase>DISABLE</dynamicFPhase>
        <clock_reset>DISABLE</clock_reset>
        <enable_reset>ENABLE</enable_reset>
        <pll_lock>ENABLE</pll_lock>
    </Page1>
    <Page2>
        <bandwidth_setting>Medium</bandwidth_setting>
    </Page2>
    <Page3>
        <setting>frequncy_setting</setting>
        <multiplication_factor>2</multiplication_factor>
        <division_factor>1</division_factor>
        <clocks>
            <clock>
                <id>0</id>
                <clock_division_factor>16</clock_division_factor>
                <clock_frequency>50.0000000000000000MHz</clock_frequency>
                <phase_shift>0.0000000000000000deg</phase_shift>
                <duty_cycle>0.5</duty_cycle>
                <clkB>false</clkB>
                <clkEN>false</clkEN>
                <bufg_option>BUFG</bufg_option>
            </clock>
            <clock>
                <id>1</id>
                <clock_division_factor>32</clock_division_factor>
                <clock_frequency>25.0000000000000000MHz</clock_frequency>
                <phase_shift>0.0000000000000000deg</phase_shift>
                <duty_cycle>0.5</duty_cycle>
                <clkB>false</clkB>
                <clkEN>false</clkEN>
                <bufg_option>NONE</bufg_option>
            </clock>
            <clock>
                <id>2</id>
                <clock_division_factor>8</clock_division_factor>
                <clock_frequency>100.0000000000000000MHz</clock_frequency>
                <phase_shift>0.0000000000000000deg</phase_shift>
                <duty_cycle>0.5</duty_cycle>
                <clkB>false</clkB>
                <clkEN>false</clkEN>
                <bufg_option>NONE</bufg_option>
            </clock>
            <clock>
                <id>3</id>
                <clock_division_factor>2</clock_division_factor>
                <clock_frequency>400.0000000000000000MHz</clock_frequency>
                <phase_shift>0.0000000000000000deg</phase_shift>
                <duty_cycle>0.5</duty_cycle>
                <clkB>false</clkB>
                <clkEN>false</clkEN>
                <bufg_option>NONE</bufg_option>
            </clock>
        </clocks>
    </Page3>
    <GeneratedFiles>
        <VHDL Enable="false">sys_pll.vhd</VHDL>
        <Verilog Enable="true">sys_pll.v</Verilog>
    </GeneratedFiles>
</PLLConfig>
