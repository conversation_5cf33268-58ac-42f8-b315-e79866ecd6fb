/************************************************************\
 **  Copyright (c) 2012-2025 Anlogic Inc.
 **  All Right Reserved.\
\************************************************************/
/************************************************************\
 ** Log	:	This file is generated by Anlogic IP Generator.
 ** File	:	D:/work/ANLOGIC_FPGA/proj/Juno_FrameWork_V3/al_ip/sys_pll/sys_pll.v
 ** Date	:	2025 09 02
 ** TD version	:	6.0.152183
************************************************************/

///////////////////////////////////////////////////////////////////////////////
//	Input frequency:                25.000000MHz
//	Clock multiplication factor: 2
//	Clock division factor:       1
//	Clock information:
//		Clock name	| Frequency 	| Phase shift
//		C0        	| 50.000000 MHZ	| 0.0000  DEG  
//		C1        	| 25.000000 MHZ	| 0.0000  DEG  
//		C2        	| 100.000000MHZ	| 0.0000  DEG  
//		C3        	| 400.000000MHZ	| 0.0000  DEG  
///////////////////////////////////////////////////////////////////////////////
`timescale 1 ns / 100 fs

module sys_pll (
  refclk,
  reset,
  lock,
  clk0_out,
  clk1_out,
  clk2_out,
  clk3_out 
);

  input refclk;
  input reset;
  output lock;
  output clk0_out;
  output clk1_out;
  output clk2_out;
  output clk3_out;

  wire clk0_buf;

  PH1_LOGIC_BUFG bufg_feedback (
    .i(clk0_buf),
    .o(clk0_out) 
  );

  PH1_PHY_PLL #(
    .DYN_PHASE_PATH_SEL("DISABLE"),
    .DYN_FPHASE_EN("DISABLE"),
    .MPHASE_ENABLE("DISABLE"),
    .FIN("25.000000"),
    .FEEDBK_MODE("NORMAL"),
    .FBKCLK("CLKC0_EXT"),
    .PLL_FEED_TYPE("EXTERNAL"),
    .PLL_USR_RST("ENABLE"),
    .GMC_GAIN(1),
    .ICP_CUR(11),
    .LPF_CAP(2),
    .LPF_RES(3),
    .REFCLK_DIV(1),
    .FBCLK_DIV(2),
    .CLKC0_ENABLE("ENABLE"),
    .CLKC0_DIV(16),
    .CLKC0_CPHASE(15),
    .CLKC0_FPHASE(0),
    .CLKC0_FPHASE_RSTSEL(0),
    .CLKC0_DUTY_INT(8),
    .CLKC0_DUTY50("ENABLE"),
    .CLKC1_ENABLE("ENABLE"),
    .CLKC1_DIV(32),
    .CLKC1_CPHASE(31),
    .CLKC1_FPHASE(0),
    .CLKC1_FPHASE_RSTSEL(0),
    .CLKC1_DUTY_INT(16),
    .CLKC1_DUTY50("ENABLE"),
    .CLKC2_ENABLE("ENABLE"),
    .CLKC2_DIV(8),
    .CLKC2_CPHASE(7),
    .CLKC2_FPHASE(0),
    .CLKC2_FPHASE_RSTSEL(0),
    .CLKC2_DUTY_INT(4),
    .CLKC2_DUTY50("ENABLE"),
    .CLKC3_ENABLE("ENABLE"),
    .CLKC3_DIV(2),
    .CLKC3_CPHASE(1),
    .CLKC3_FPHASE(0),
    .CLKC3_FPHASE_RSTSEL(0),
    .CLKC3_DUTY_INT(1),
    .CLKC3_DUTY50("ENABLE"),
    .INTPI(0),
    .HIGH_SPEED_EN("DISABLE"),
    .SSC_ENABLE("DISABLE"),
    .SSC_MODE("CENTER"),
    .SSC_AMP(0.0000),
    .SSC_FREQ_DIV(0),
    .SSC_RNGE(0),
    .FRAC_ENABLE("DISABLE"),
    .DITHER_ENABLE("DISABLE"),
    .SDM_FRAC(0) 
  ) pll_inst (
    .refclk(refclk),
    .pllreset(reset),
    .lock(lock),
    .pllpd(1'b0),
    .refclk_rst(1'b0),
    .wakeup(1'b0),
    .psclk(1'b0),
    .psdown(1'b0),
    .psstep(1'b0),
    .psclksel(3'b000),
    .psdone(sys_pll_open69),
    .cps_step(2'b00),
    .drp_clk(1'b0),
    .drp_rstn(1'b1),
    .drp_sel(1'b0),
    .drp_rd(1'b0),
    .drp_wr(1'b0),
    .drp_addr(8'b00000000),
    .drp_wdata(8'b00000000),
    .drp_err(sys_pll_open70),
    .drp_rdy(sys_pll_open71),
    .drp_rdata({sys_pll_open79, sys_pll_open78, sys_pll_open77, sys_pll_open76, sys_pll_open75, sys_pll_open74, sys_pll_open73, sys_pll_open72}),
    .fbclk(clk0_out),
    .clkc({sys_pll_open90, sys_pll_open88, sys_pll_open86, sys_pll_open84, clk3_out, clk2_out, clk1_out, clk0_buf}),
    .clkcb({sys_pll_open91, sys_pll_open89, sys_pll_open87, sys_pll_open85, sys_pll_open83, sys_pll_open82, sys_pll_open81, sys_pll_open80}),
    .clkc_en({8'b00001111}),
    .clkc_rst(2'b00),
    .ext_freq_mod_clk(1'b0),
    .ext_freq_mod_en(1'b0),
    .ext_freq_mod_val(17'b00000000000000000),
    .ssc_en(1'b0) 
  );

endmodule

