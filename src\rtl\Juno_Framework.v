`timescale 1ns / 1ps
//-------------------------------------------------------------------------------------------------------------------------------
//Copyright (c)  All rights reserved 
//-------------------------------------------------------------------------------------------------------------------------------
//Author : QHYCCD YangSK
//File   : Juno_Framework
//Create : 2024-06-03 
//Revise : 2024-06-03 
//Editor : Vscode, tab size (4)
//VERSION: 
//Devices: 
//Functions: 
//I_XXXX: input to FPGA
//O_XXXX: output to FPGA
//IO_XXX；inout FPGA
//All the signals in the top layer are the same as the network signal names in the schematic
//Additional Comments:
//-------------------------------------------------------------------------------------------------------------------------------

//`define UART_DEBUG_AS_DDR4 115200bps 
`define UART_DEBUG_AS_FX10 ////921600bps
`define DDR3ADDRE_MAXBIT     28  

module Juno_Framework( 

    //SYSTEM CLOCK  & RST 
    input   wire          I_SYSCLK       ,//N18 v1 pcb  25M
    input   wire          I_DDR_REFCLK   ,//v1 pcb 133.333M
   
    //CMOS control 
    input   wire          I_I2CSCL       ,//V19
    inout   wire          IO_I2CSDA      ,//
    inout   wire          IO_CMOS_CTL1   ,//
    inout   wire          IO_CMOS_CTL2   ,//
    inout   wire          IO_CMOS_CTL3   ,//
    inout   wire          IO_CMOS_CTL4   ,//
    inout   wire          IO_CMOS_CTL5   ,//
    inout   wire          IO_XHS         ,//
    inout   wire          IO_XVS         ,//

    output  wire          O_INCK         ,//
    output  wire          O_XMASTER      ,//
    output  wire          O_XTRIG        ,//
    output  wire          O_XCLR         ,//
    output  wire          O_TEC_EN       ,//
    output  wire          O_AMPV         ,//

    //FX10
    input   wire          I_FX10_RESET   ,//
    input   wire  [6:0]   I_P0IOX        ,//INPUT PORT P0 IO lvcmos :0-6:D15,D16,C17,C18,E17,E18,E16
    output  wire          O_P0CLK_P      ,//OUTPUT PORT P0 CLK  LVDS: B15
    output  wire  [7:0]   O_P0DX_P       ,//OUTPUT PORT P0 data lvds:0-7:G15,C12,F15,A13,C13,E14,C14,b16,
    output  wire          O_P0C_P        ,//OUTPUT PORT P0 CONCTRL LVds B17
    
    input   wire  [6:0]   I_P1IOX        ,//INPUT PORT  P1 IO lvcmos :0-6:G8,F8,D9,C9,B11,B10,A9
    output  wire          O_P1CLK_P      ,//OUTPUT PORT P1 CLK  LVDS:H14 
    output  wire  [7:0]   O_P1DX_P       ,//OUTPUT PORT P1 data lvds:0-7:D10,G13,E13,F11,E11,G11,H12,F9
    output  wire          O_P1C_P        ,//OUTPUT PORT P1 CONCTRL LVDS h9
    
    input   wire          I_UART_TX_FX10 ,//INPUT TO FPGA UART ,fx10 tx,A10
    output  wire          O_UART_RX_FX10 ,//OUTPUT TO FX10 UART RX,A11
   
    //MIPI 

    //Others
    input   wire          I_TRIG_IN_IO    ,//
    output  wire          O_TRIG_OUT_IO   ,//

    //Filter wheel          
    input   wire         I_UART_RX_FPGA   ,//
    output  wire         O_UART_TX_FPGA   ,//
    //GPS 
    input   wire         I_TRIGIN         ,//
    input   wire         I_GPSDATA        ,//
    output  wire         O_V              ,//
    output  wire         O_LED_CMOS       ,//
    output  wire         O_INCK_GPS       ,//


    //GPIO 1.8v
    inout    wire         IO_GPIO1_IO     ,//V21
    inout    wire         IO_GPIO2_IO     ,//
    inout    wire         IO_GPIO3_IO     ,//
    inout    wire         IO_GPIO4_IO     ,//
    //GPIO 3.3V
    inout    wire         IO_GPIO5_IO     ,// inout FAN_PWM 
    inout    wire         IO_GPIO6_IO     ,// inout FAN_FG
    inout    wire         IO_GPIO7_IO     ,//
    inout    wire         IO_GPIO8_IO     , //

    //DDR 
    output                     ddr_ten     ,
    output                     ddr_parity  ,
    output                     ddr_alert_n ,
    //DDR SIGNALS
    //input                      ddr_alert_n, //Set as 0
    inout    [DM_WIDTH-1:0]    ddr_dm_dbi ,
    inout    [DQS_WIDTH-1:0]   ddr_dqs_c  ,
    inout    [DQS_WIDTH-1:0]   ddr_dqs_t  ,
    inout    [DQ_WIDTH-1:0]    ddr_dq     ,

    output   [ADDR_WIDTH-1:0]  ddr_addr   ,
    output   [BANK_WIDTH-1:0]  ddr_ba     ,
    output   [CKE_WIDTH-1:0]   ddr_cke    ,
    output   [ODT_WIDTH-1:0]   ddr_odt    ,
    output   [CS_WIDTH-1:0]    ddr_cs_n   ,
    output                     ddr_reset_n,
    output                     ddr_ck_t   ,
    output                     ddr_ck_c   ,
    output   [BG_WIDTH-1:0]    ddr_bg     ,
    output                     ddr_act_n  
  //output                     ddr_parity ,
  //output                     ddr_ten   

);
 
     parameter ODT_WIDTH           = 1       ;
     parameter CKE_WIDTH           = 1       ; // # of cke outputs
     parameter CS_WIDTH            = 1       ; // # of unique CS outputs
     parameter DQ_WIDTH            = 32      ;
     parameter DQS_WIDTH           = 4       ; // # of DQS (strobe)
     parameter DM_WIDTH            = 4       ;
     parameter ADDR_WIDTH          = 17      ;
     parameter BANK_WIDTH          = 2       ;
     parameter BG_WIDTH            = 1       ;

     parameter ROW_WIDTH           = 15      ;
     parameter COL_WIDTH           = 10      ;
     parameter ECC                 = "OFF"   ;
     parameter MC_ADDR_WIDTH       = ROW_WIDTH + COL_WIDTH + BANK_WIDTH + BG_WIDTH;
     parameter AXI_ID_WIDTH        = 4;
     parameter AXI_ADDR_WIDTH      = MC_ADDR_WIDTH + 2;
     parameter MC_DATA_WIDTH       = (ECC == "ON")?( DQS_WIDTH-1)*8*8 : DQS_WIDTH*8*8;
     parameter MC_MASK_WIDTH       = (ECC == "ON")?( DQS_WIDTH-1)*8   : DQS_WIDTH*8  ;
     parameter AXI_DATA_WIDTH      = MC_DATA_WIDTH;



//CLOCK signals
wire    syspll_locked   ;
wire    mipi_lp_clk     ;
wire    clk1_out_25m      /*synthesis keep*/  ;

//ddr signals 
wire                                      I_ddr_rstn/*synthesis keep*/; 
wire                                      O_dfi_clk; 
wire    [7:0]                             O_ddr_states; 

// mipi signals
wire                                      mipi_rst; 
wire                                      mipi_err/*synthesis keep*/; 
                                	                                                                               	
//fpga information                      	
wire	[7:0]		year		     		;
wire	[7:0]		month		        	;
wire	[7:0]		day			        	;
wire	[7:0]		subversion1	        	;
wire	[7:0]		subversion2	        	;
wire	[7:0]		boardty             	;

//OTHERS 
wire	[1:0]		asmi_flashcs            ;
wire	[23:0]		asmi_address		    ;	
wire    [`DDR3ADDRE_MAXBIT:0] 	threshold_num 	;//default is 
wire    [15:0] 	    VMAX_2_LSB			    ;     
wire    [23:0]		FilterTime			    ;                                         
wire    [39:0] 	    ExpTime				    ;   
wire    [31:0] 	    TFPP_time			    ;       
wire    [31:0] 	    TRPP_time			    ; 
wire    [31:0]		PatchVNumber    	    ;
wire    [7 :0]  	BurstStart	    	    ;
wire    [15:0]  	BurstEnd  	    	    ;                                 
wire    [31:0] 	    VMAX				    ;                                          
wire    [31:0] 	    HMAX				    ;                                
wire    [31:0] 	    AMPV_START			    ;                                          
wire    [31:0] 	    AMPV_END			     /*synthesis keep*/ ;   
wire    [`DDR3ADDRE_MAXBIT:0] 	rema_num  	;
wire    [15:0]		DetectedBw				;  
wire	[15:0]		DetectedXSize			;     
wire	[15:0]		DetectedYSize			; 


wire                I2CSDA_0_IN             ;
wire                I2CSDA_0_OUT            ;
wire                I2CSDA_0_OE             ;
//=======================================================debug code start ==========================================================//
/*
reg [9:0] cnt=0;
always @(posedge I_SYSCLK) begin
    cnt<=cnt+1;
end

assign IO_GPIO1_IO= cnt[9];
*/
`ifdef UART_DEBUG_AS_DDR4//115200bps
wire  uart_rxd ;//IO_GPIO5_IO input 
wire  uart_txd ;//IO_GPIO6_IO outpu 

assign  IO_GPIO5_IO= 1'bz;
assign uart_rxd= IO_GPIO5_IO;
assign  IO_GPIO6_IO=uart_txd;

`elsif   UART_DEBUG_AS_FX10//921600bps

assign  IO_GPIO6_IO=I_UART_TX_FX10; 
assign  IO_GPIO1_IO= I_I2CSCL;
`endif 

assign I_ddr_rstn=1'b1;
//=======================================================debug code end  ==========================================================//


//================================================gpif tx start ============================================//
wire [7:0] gpif_state;//reg232
wire  	   clk_DataRate_1_8;
wire       clk_DataRate_1_2;
// FxnTxTop i2c register control signals
wire        fxn_training_en;                    // [1:0] input  - Training enable signal
wire        fxn_p0_training_completed;          // [1:0] input  - P0 port training completion status
wire        fxn_p1_training_completed;          // [1:0] input  - P1 port training completion status
wire [1:0]  fxn_thread_addr;                    // [1:0] input  - Thread address (STAD)
wire [2:0]  fxn_socket_addr;                    // [2:0] input  - Socket address (SSAD)
wire        fxn_reset_stop_stream_req;          // [1:0] input  - Reset or stop stream request
wire        fxn_rst_n;
// FxnTxTop upstream data stream signals
wire [127:0] fxn_upstream_data;                 // [127:0] input - Upstream data stream (16 lanes * 8 bits)
wire        fxn_upstream_data_valid;            // [1:0] input  - Upstream data valid signal
wire        fxn_datawr_ready;                   // [1:0] output - Data write ready signal


//================================================FxnTxTop Instantiation Start============================================//
// FxnTxTop module instantiation - FX10/FX20 GPIF transmission control module
// Parameter configuration:
//   GPIF_M_CLK_VALUE_HZ: GPIF master clock frequency 100MHz
//   WriteBufferSizeByte: Write buffer size 64512 bytes
//   FrameHeadEnd: Frame header end identifier 64'hEE11DD11223344
//   TX_SERIALIZER_LSB_FIRST_I: Serializer bit order configuration 1 is fx10/20 msb first
FxnTxTop #(
    .GPIF_M_CLK_VALUE_HZ        (100_000_000        ),  // [31:0] param - GPIF master clock frequency 100MHz
    .WriteBufferSizeByte        (64512              ),  // [31:0] param - Write buffer size 64512 bytes
    .FrameHeadEnd               (64'hEE11DD22_11223344 ),  // [63:0] param - Frame header end identifier
    .TX_SERIALIZER_LSB_FIRST_I  (1                  )   // [31:0] param - Serializer LSB first configuration
) FxnTxTop_inst (
    // Clock and reset signals
    .clk_DataRate_1_8           (clk_DataRate_1_8      ), // [1:0]   input  - GPIF master clock 100MHz
    .clk_DataRate_1_2           (clk_DataRate_1_2   ), // [1:0]   input  - GPIF clock 1/2 rate (same as 1/8 for now)
    .rst_n                      (fxn_rst_n          ), // [1:0]   input  - Active low reset signal
    
    // I2C register control signal group
    .training_en                (fxn_training_en        ), // [1:0]   input  - Training enable signal
    .p0_training_completed      (fxn_p0_training_completed), // [1:0] input  - P0 port training completion status
    .p1_training_completed      (fxn_p1_training_completed), // [1:0] input  - P1 port training completion status
    
    // Frame manager control signal group
    .thread_addr_i              (fxn_thread_addr        ), // [1:0]   input  - Thread address STAD
    .socket_addr_i              (fxn_socket_addr        ), // [2:0]   input  - Socket address SSAD  
    .ResetOrStop_stream_req_i   (fxn_reset_stop_stream_req), // [1:0] input  - Reset or stop stream request
    
    // Training sequence configuration signal group (per lane)
    .phy_training_seq           (8'h39   ), // [7:0]   input  - PHY layer training sequence
    .link_training_seq_p0       (8'h55	 ), // [7:0]  input  - P0 link training sequence
    .link_training_seq_p1       (8'haa	 ), // [7:0]  input  - P1 link training sequence
    .link_training_seq_p2       (8'h55	 ), // [7:0]  input  - P2 link training sequence
    .link_training_seq_p3       (8'haa	 ), // [7:0]  input  - P3 link training sequence
    
    // Upstream data stream signal group
    .upstream_data_i            (fxn_upstream_data      ), // [127:0] input  - Upstream data stream 16 lanes × 8 bits
    .upstream_data_valid_i      (fxn_upstream_data_valid), // [1:0]   input  - Upstream data valid signal
    
    // FX10 port status signal group
    .fxn_p0_buffer_ready_i      (I_P0IOX[5]    ), // [1:0]   input  - P0 port DMA buffer ready
    .fxn_p0_rx_link_ready_i     (I_P0IOX[6]   ), // [1:0]   input  - P0 port LVDS link ready
    .fxn_p1_buffer_ready_i      ( 0  ), // [1:0]   input  - P1 port DMA buffer ready (unused)
    .fxn_p1_rx_link_ready_i     ( 0  ), // [1:0]   input  - P1 port LVDS link ready (unused)
    
    // Output signal group
    .dataWr_ready_o             (fxn_datawr_ready       ), // [1:0]   output - Data write ready status signal
    .lvdsClk_DataRate_1_8       (O_P0CLK_P				), // [1:0] output - LVDS clock output 1/8 data rate
    .dout_to_pins_ctrl          ({O_P1C_P,O_P0C_P}  ), // [1:0]   output - Control signal to pins output
    .dout_to_pins_data          ({O_P1DX_P,O_P0DX_P} )  // [15:0]   output - Data signal to pins output
);
//================================================FxnTxTop Instantiation End============================================//


//================================================gpif tx end ============================================//


     assign ddr_ten =  1'b0;
     assign ddr_parity = 1'b0;
     assign ddr_alert_n =1'b1;
assign IO_I2CSDA= I2CSDA_0_OE?I2CSDA_0_OUT:1'bz;
assign I2CSDA_0_IN=IO_I2CSDA;

 sys_pll sys_pll_inst(
    .refclk         (I_SYSCLK       ),
    .reset          (1'b0           ),

    .lock           (syspll_locked  ),
    .clk0_out       ( mipi_lp_clk   ),//50mCLK
    .clk1_out       (clk1_out_25m   ),//25mHZ
	.clk2_out 		(clk_DataRate_1_8  ), //100mHZ
	.clk3_out 		(clk_DataRate_1_2  ) //100mHZ
   
);

MipiRx_Decode MipiRx_Decode_inst ( 
    .mipi_lp_clk    (mipi_lp_clk    ), 
    .mipi_rst       (mipi_rst       ), 
    .mipi_err       (mipi_err       )
); 
//


DDr_UiMig #( 
    .ODT_WIDTH      (ODT_WIDTH      ), 
    .CKE_WIDTH      (CKE_WIDTH      ), 
    .CS_WIDTH       (CS_WIDTH       ), 
    .DQ_WIDTH       (DQ_WIDTH       ), 
    .DQS_WIDTH      (DQS_WIDTH      ), 
    .DM_WIDTH       (DM_WIDTH       ), 
    .ADDR_WIDTH     (ADDR_WIDTH     ), 
    .BANK_WIDTH     (BANK_WIDTH     ), 
    .BG_WIDTH       (BG_WIDTH       ), 
    .ROW_WIDTH      (ROW_WIDTH      ), 
    .COL_WIDTH      (COL_WIDTH      ), 
    .ECC            (ECC            ), 
    .MC_ADDR_WIDTH  (MC_ADDR_WIDTH  ), 
    .AXI_ID_WIDTH   (AXI_ID_WIDTH   ), 
    .AXI_ADDR_WIDTH (AXI_ADDR_WIDTH ), 
    .MC_DATA_WIDTH  (MC_DATA_WIDTH  ), 
    .MC_MASK_WIDTH  (MC_MASK_WIDTH  ), 
    .AXI_DATA_WIDTH (AXI_DATA_WIDTH ) 
) 
DDr_UiMig_INST (  //synthesis keep
    .I_ddr_refclk   (I_DDR_REFCLK   ), 
    .I_ddr_rstn     (I_ddr_rstn     ), 
    .O_dfi_clk      (O_dfi_clk      ), 
    .O_ddr_states   (O_ddr_states   ), 

    //user Interface
    .wr_clk         (   ),
    .wrddr4_data    (   ),
    .wrddr4_en      (   ),
    .rd_clk         (   ),
    .rddr4_en       (   ),
    .rddr4_data     (   ),  
    .rddr4_vld      (   ),
    .DDR_CLG        (   ),  
    .flow_control   (   ),
    .threshold_num  (   ),  
    .rema_num       (   ),  

     //debug signals                      
    .uart_rxd       (uart_rxd       ),//input   
    .uart_txd       (uart_txd       ),//output  

    //.ddr_alert_n    ( ddr_alert_n_1   ), // ddr_alert_n
    .ddr_dm_dbi     (ddr_dm_dbi     ), 
    .ddr_dqs_c      (ddr_dqs_c      ), 
    .ddr_dqs_t      (ddr_dqs_t      ), 
    .ddr_dq         (ddr_dq         ), 
    .ddr_addr       (ddr_addr       ), 
    .ddr_ba         (ddr_ba         ), 
    .ddr_cke        (ddr_cke        ), 
    .ddr_odt        (ddr_odt        ), 
    .ddr_cs_n       (ddr_cs_n       ), 
    .ddr_reset_n    (ddr_reset_n    ), 
    .ddr_ck_t       (ddr_ck_t       ), 
    .ddr_ck_c       (ddr_ck_c       ), 
    .ddr_bg         (ddr_bg         ), 
    .ddr_act_n      (ddr_act_n      )
    //.ddr_parity     (     )//ddr_parity
); 
//
   


 fpga_info	fpga_info_inst(

		.year					(year		    ),
		.month					(month		    ),
		.day					(day			),
		.subversion1			(subversion1	),
		.subversion2			(subversion2	),
		.boardty     			(boardty        )
	
);

reg [2:0] rst_t=3'b111 ;
always @ (posedge mipi_lp_clk)begin
    rst_t<={rst_t[1:0],1'b0};
end 

i2cSlave 	i2cSlave0_inst(

		.clk					(mipi_lp_clk   ),//mipi_lp_clk clk1_out_25m
		.rst					(rst_t[2]		),
		.sdaIn					(I2CSDA_0_IN	),
		.scl					(I_I2CSCL		),
		.sdaOut  				(I2CSDA_0_OUT	),
		.sda_oe      			(I2CSDA_0_OE	),        
		                	                              
		.myreg00				(DetectedXSize[15:8]),
		.myreg01				(DetectedXSize[7:0]),
		.myreg02				(DetectedYSize[15:8]),
		.myreg03				(DetectedYSize[7:0]),
		.myreg04				(rema_num[12:5]),///test  fx3txnum64[7:0] rema_num is byte ,set it to 256bit,is X <<3 >>8=X>>5     		       		
		.myreg05				(rema_num[20:13]),//fx3txnum64[15:8]
		.myreg06				(rema_num[`DDR3ADDRE_MAXBIT:21]),//fx3txnum64[23:16]
		
	                    	 	           		       	
		.myreg28				(), //test  fx3txnum64[31:24]
		
		.myreg29				(),//not use 
		.myreg30				(), //not  use  
		.myreg31				(), //not use
		.myreg32				(), //not use  
		.myreg33				(), //not use  
		.myreg41				(DetectedBw[7:0]),//
		.myreg42				(DetectedBw[15:8]), // 
		
		.myreg52				(fpga_state1	),
		.myreg53				(fpga_state2	),
		.myreg54				(fpga_state3	),//fpga_state3
		.myreg55				(	),//fpga_state4
		.myreg56				(	),//fpga_state_indicate
		            
		.myreg200				(year), //FPGA verson year
		.myreg201				(month), //month
		.myreg202				(day), //day
		.myreg203				(subversion1), //subversion1
		.myreg204				(boardty), //bord type
		.myreg205				(asmi_dataout),
		.myreg206				(),
		.myreg207				(subversion2),//subversion2
        .myreg210				(rstn_num),                	           
                                                                                                         
		.reg00					(i2c_xclr),
		.reg01					({skip_check, regddr_rstn}),// 2ms
		.reg02					(),
		.reg03					(is16bit),
		.reg04					(guide1_gps_ctl),// 
		.reg05					(guide2),
		.reg06					(guide3),
		.reg07					(guide4),
		.reg08					(ampv_enable),// 
		.reg09					(AMPV_END[15:8]), //8
		.reg10					(AMPV_END[7:0]),
		.reg11					(),
		.reg12					(AMPV_END[31:24]),
		.reg13					(AMPV_END[23:16]),
		.reg14					(AMPV_START[15:8]),
		.reg15					(AMPV_START[7:0]),
		.reg16					(AMPV_START[31:24]),
		.reg17					(AMPV_START[23:16]),
		.reg18					(gain_18),//red
		.reg19					(gain_19),//green
		.reg20					(gain_20),//green 
		.reg21					(gain_21),//blue 
		.reg22					(VMAX[31:24]),
		.reg23					(VMAX[23:16]),
		.reg24					(VMAX[15:8]),
		.reg25					(VMAX[7:0]),
		.reg26					(HMAX[31:24]),//8
		.reg27					(HMAX[23:16]),
		.reg28  				(HMAX[15:8]),
		.reg29					(HMAX[7:0]),
		.reg30			        (isddr),
		.reg31					(xpatch),//
		.reg32					(),//8
		.reg34					(),//1
		.reg35					(IDLE),
		.reg36					(LoopExp),//1
		.reg37					(),//8
		.reg38					(),
		.reg39					(test_mode),
		.reg40					(LED_control),
		.reg41					(PatchVNumber[31:24]),
		.reg42					(PatchVNumber[23:16]),
		.reg43					(PatchVNumber[15:8]),
		.reg44					(PatchVNumber[7:0]),
		.reg45					(VMAX_2_LSB[15:8]),		
		.reg46					(VMAX_2_LSB[7:0]),	//8
	                        	
		.reg49					(AMPV_MANUAL),//1       //manual AMPV                           		                            		
		.reg50					(BurstStart),//8
		.reg51					(BurstEnd[15:8]),
		.reg52					(BurstEnd[7:0]),
		.reg53					(gain_mode),
		.reg54					(ac_mipi_rstn),//ac_mipi_rstn 1ms 
		.reg55					(),//8
		.reg56					(FrameNumEn),//1       //FRAME COUNTER ENABLE
		.reg57					(EnableBurstMode),//1
		.reg58					(TrigMode),//8
		.reg59					(),
		.reg60					(),
		.reg61					(),
		.reg62					(DecodeMode),//DecodeMode
		.reg63					(DDR_CLG_R),
		.reg64					(),
		.reg65					(threshold_num[7:0]),
		.reg66					(threshold_num[15:8]),
		.reg67					(threshold_num[23:16]),
		.reg68					(threshold_num[`DDR3ADDRE_MAXBIT:24]),
		.reg69					(),
		.reg70					(),
		.reg71					(),
		.reg72					(),
		.reg73					(),
		.reg74					(),
		.reg75					(),//8
		.reg76					(),//2
		.reg77					(),//2
		.reg79					(CmosCtrlMode),
    	                    	                                                
		.reg80					(),//8  //#1 PLL Dynamic Phase Adjustment C0-C4 Select
		.reg81					(),//1		   //direction
		.reg82					(),        //run
		.reg83					(),//1        //pll reset
		.reg84					(),//8  //#2 PLL DPA C0-C4
		.reg85					(),//1        //direction
		.reg86					(),        //run
		.reg87					(),//1        //pll reset
		.reg88					(),//8  //LVDS channel Select
		.reg89					(watchdogenable),//1
	                    		
		.reg91					(),//8  //LVDS input phase delay time
		.reg92					(),//1        //LVDS input phase delay time WR  0->1 action
		.reg93					(),//8  //LVDS bit shift. bit select position
		.reg94					(),//1        //LVDS bit select WR  0->1 action
		.reg95					(),//1        //LVDS input phase detector exe  0:reset/clear  1:run  
		.reg96					(),//8
		.reg97					(),//1
		.reg98					(),//1
		       
		.reg1x03             	(driver_feed_dogs),
		.reg1X05				(),//8  //asmi_addr[31- T35 not use 
		.reg1X06				(asmi_address[23:16]),  //asmi_addr[23-
		.reg1X07				(asmi_address[15:8]),  //asmi-addr[15-
		.reg1X08				(asmi_address[7:0]),//8  //asmi-addr[7-
		.reg1X09				(),//1         //Bulk_earse  not use 
		.reg1X10				(),//8   //datain [7-] not use 
		.reg1X11				(asmi_rden),//1
		.reg1X12				(asmi_readstart),
		.reg1X13				(),//not use
		.reg1X14				(), //not use 
		.reg1X15				(asmi_reset),
		.reg1X16				(asmi_erasestart),
		.reg1X17				(asmi_wpen),//
		.reg1X18				(),//not use 
		.reg1X19				(),//not use 
		.reg1X20				(),//1
	                        	
		.reg1X29				(),//not use 8
		.reg1X30				(),//not use 
		.reg1X31				(),//not use 
		.reg1X32				(asmi_flashcs[1:0]),//
		.reg1X33				(remote_sel),//8
		.reg1X34				(),//not use 1
		.reg1X35				(asmi_num),//8
		.reg1X36				(remote_reconfig),//1
		.reg1X37				(),//not use
		.reg1X38				(),//not use
		.reg1X39				(),//not use
		.reg1X40				(asmi_writestart),//1
		.reg1X41				(),//8   not use    
		.reg1X42				(TrigSignalEn),  
		.reg1X43                (AMPV_MODE),
		.reg1X46				(FilterTime[23:16]),
		.reg1X47				(FilterTime[15:8]),
		.reg1X48  				(FilterTime[7:0]),		
		.reg1X52				(Xtrig_rstn),
		.reg1X53       			(TrigModeA),     
		.reg1X54				(ExpTime[39:32]),
		.reg1X55				(ExpTime[31:24]),
		.reg1X56				(ExpTime[23:16]),    
		.reg1X57				(ExpTime[15:8]),    
		.reg1X58				(ExpTime[7:0]),   
		.reg1X59				(TFPP_time[7:0]),//TFPP_time
		.reg1x60 				(TFPP_time[15:8]),
		.reg1x61 				(TFPP_time[23:16]), 
		.reg1x62 				(TFPP_time[31:24]),
		.reg1x63 				(TRPP_time[7:0]),//TRPP_time
		.reg1x64 				(TRPP_time[15:8]),
		.reg1x65 				(TRPP_time[23:16]),
		.reg1x66				(TRPP_time[31:24]),

		//.reg232 				(gpif_state),//8 gpif_state; gpif tx state register

		.reg2X55				(),//8 not use 
		.AwriteEn				(asmi_divld),//1
		.AregAddr				(),//8
		.AdataToRegIF			(asmi_didata) //8

);
//i2cSlave

endmodule
