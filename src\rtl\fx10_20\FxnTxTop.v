`timescale 1ns/1ps
`timescale 1ns / 1ps
//-------------------------------------------------------------------------------------------------------------------------------
//Copyright (c)  All rights reserved 
//-------------------------------------------------------------------------------------------------------------------------------
//Author : QHYCCD YangSK
//File   : FxnTxTop
//Create : 2025-09-02 
//Revise : 2025-09-02 
//Editor : Vscode, tab size (4)
//VERSION: 
//Devices: 
//Functions: 
//Additional Comments:
//
//-------------------------------------------------------------------------------------------------------------------------------

module FxnTxTop#(

    parameter GPIF_M_CLK_VALUE_HZ = 100_000_000,
    parameter WriteBufferSizeByte = 64512,
    parameter FrameHeadEnd = 64'hEE11DD22_11223344,
    parameter TX_SERIALIZER_LSB_FIRST_I = 1 //1 is fx10msb first, FPGA Serializer needs LSBit first and FX10 needs MSBit first

)
(
    input          clk_DataRate_1_8,                // System clock (from FXn) — used as 'clk' for FSMs
    input wire     clk_DataRate_1_2,
    input wire     rst_n,                         // Active-low reset

// I2C registers begin
    input wire     training_en,                  // allow training (DevManage_fsm)
    input wire     p0_training_completed,          // From device config (FX10/FX20), I2C register;         
    input wire     p1_training_completed,          // From device config (FX10/FX20), I2C register;         
// FrameManager controls
    input wire [1:0] thread_addr_i,            // Thread address (STAD)
    input wire [2:0] socket_addr_i          ,  // Socket address (SSAD)
    input wire       ResetOrStop_stream_req_i, // Stop stream request
// Training sequences (per-lane)
    input wire [7:0] phy_training_seq       ,
    input wire [7:0] link_training_seq_p0   ,
    input wire [7:0] link_training_seq_p1   ,
    input wire [7:0] link_training_seq_p2   ,
    input wire [7:0] link_training_seq_p3   ,
// I2C registers end

// Upstream payload source (to FrameManager)
    input wire [TOTAL_LVDS_DATA_LANES*LVDS_TX_GEAR_RATIO-1:0] upstream_data_i,
    input wire                                                upstream_data_valid_i,
    input wire     fxn_p0_buffer_ready_i,     // DMA Full/Ready (P0)
    input wire     fxn_p0_rx_link_ready_i,      // LVDS Link Ready (P0)
    input wire     fxn_p1_buffer_ready_i,     // DMA Full/Ready (P1) not used
    input wire     fxn_p1_rx_link_ready_i,    // LVDS Link Ready (P1) not used

// Outputs
    output wire         dataWr_ready_o      ,
    output wire         lvdsClk_DataRate_1_8,
    output wire [1:0]    dout_to_pins_ctrl   ,
    output wire [15:0]    dout_to_pins_data
);

// Global parameters (kept consistent with your submodules)
parameter  LVDS_TX_GEAR_RATIO = 8;
parameter  GPIF_MASTER_MODE_I = "LVDS";
parameter TOTAL_LVDS_DATA_LANES = 16;  // Matches FxnTxLvds( data_i[127:0] )
parameter LVDS_PHY_TS_TIME_IN_US_I = 50;
parameter LINK_TRAINING_COUNT_I = 10;
parameter IDLE_TIME_AFTER_TRAINING_IN_US_I = 3;
// Clock / Reset 
wire fxn_gpif_m_clk=clk_DataRate_1_8; // LVDS domain clk @ data rate of 1/8 


// Low Power mode controls,not use
wire enter_l1_mode_i=0;                // Enter L1 request
wire enter_l2_mode_i=0;                // Enter L2 request
wire exit_l1_mode_i=0;                 // Exit L1 request
wire exit_l2_mode_i=0;                 // Exit L2 request

// -----------------------------------------------------------------------------
// Inter-module wires
// -----------------------------------------------------------------------------
wire        start_phylink_training;   // DevManage -> LvdsTraining
wire        start_serving_device_pl;  // DevManage -> FrameManager
wire        phylink_training_done;    // LvdsTraining -> DevManage

// Frame Manager -> DataNCtrl
wire [LVDS_TX_GEAR_RATIO-1:0]                    dp_ctrl_byte;
wire                                             dp_ctrl_byte_valid;
wire [TOTAL_LVDS_DATA_LANES*LVDS_TX_GEAR_RATIO-1:0] dp_data_byte;
wire                                             dp_data_byte_valid;

// Training -> DataNCtrl
wire [LVDS_TX_GEAR_RATIO-1:0] train_ctrl_byte;
wire                           train_ctrl_byte_valid;
wire [LVDS_TX_GEAR_RATIO-1:0] train_data_byte;
wire                           train_data_byte_valid;

// DataNCtrl -> Serializer
wire [LVDS_TX_GEAR_RATIO-1:0]                      ctrl_byte_8b;  // 8-bit control per byte time
wire [TOTAL_LVDS_DATA_LANES*LVDS_TX_GEAR_RATIO-1:0] wr_data_128b;// 128-bit data bus

wire [15:0] control_16b= {8'b0, ctrl_byte_8b};

// -----------------------------------------------------------------------------
// DevManage_fsm — overall flow control (training then frame transfer)
// -----------------------------------------------------------------------------
DevManage_fsm #(
    .GPIF_MASTER_MODE_I     (GPIF_MASTER_MODE_I),
    .TOTAL_DATA_LANES_I     (TOTAL_LVDS_DATA_LANES)
) u_DevManage_fsm (
    .clk                        (fxn_gpif_m_clk),         // (in,1)   clock
    .reset_n                    (rst_n),                  // (in,1)  active-low reset

    //.fxn_buffer_ready_i       (),                         // (in,1)  reserved
    .fxn_lvds_rx_link_ready_i   (fxn_p0_rx_link_ready_i), // (in,1)  LVDS PHY ready 
    .phylink_training_done_i    (phylink_training_done),    // (in,1) from LvdsTraining_fsm

    // Low Power Mode
    .enter_l1_mode_i            (enter_l1_mode_i),          // (in,1)
    .enter_l2_mode_i            (enter_l2_mode_i),          // (in,1)
    .exit_l1_mode_i             (exit_l1_mode_i),           // (in,1)
    .exit_l2_mode_i             (exit_l2_mode_i),           // (in,1)

    // Register Info (I2C)
    .training_en                (training_en),              // (in,1)
    .p0_training_completed      (p0_training_completed),    // (in,1)
    .p1_training_completed      (p1_training_completed),    // (in,1)

    // Outputs
    .start_phylink_training_o   (start_phylink_training),   // (out,1) to LvdsTraining_fsm
    .start_serving_device_o     (start_serving_device_pl),  // (out,1) to FrameManager_fsm
    .enter_l1_lp_mode_o         (),                         // (out,1) unused
    .enter_l2_lp_mode_o         (),                         // (out,1) unused
    .exit_l1_lp_mode_o          (),                         // (out,1) unused
    .exit_l2_lp_mode_o          ()                          // (out,1) unused
);

// -----------------------------------------------------------------------------
// LvdsTraining_fsm — PHY & Link training byte generator
// -----------------------------------------------------------------------------
LvdsTraining_fsm #(
    .LVDS_TX_GEAR_RATIO                 (LVDS_TX_GEAR_RATIO),
    .GPIF_M_CLK_VALUE_HZ                (GPIF_M_CLK_VALUE_HZ),       
    .GPIF_MASTER_MODE_I                 (GPIF_MASTER_MODE_I),
    .DATA_WIDTH_IN_BITS_I               (32),   
    .LVDS_PHY_TS_TIME_IN_US_I           (LVDS_PHY_TS_TIME_IN_US_I),             
    .LINK_TRAINING_COUNT_I              (LINK_TRAINING_COUNT_I),               
    .IDLE_TIME_AFTER_TRAINING_IN_US_I   (IDLE_TIME_AFTER_TRAINING_IN_US_I)                  
) u_LvdsTraining_fsm (
    .clk                        (fxn_gpif_m_clk),          // (in,1)
    .reset_n                    (rst_n),                   // (in,1)

    // Inputs
    .start_phylink_training_i   (start_phylink_training),   // (in,1) from DevManage_fsm
    .phy_training_seq           (phy_training_seq),         // (in,8) I2C reg
    .link_training_seq_p0       (link_training_seq_p0),     // (in,8) I2C reg
    .link_training_seq_p1       (link_training_seq_p1),     // (in,8) I2C reg
    .link_training_seq_p2       (link_training_seq_p2),     // (in,8) I2C reg
    .link_training_seq_p3       (link_training_seq_p3),     // (in,8) I2C reg

    // Outputs
    .train_ctrl_byte_o          (train_ctrl_byte),          // (out,8)
    .train_ctrl_byte_valid_o    (train_ctrl_byte_valid),    // (out,1)
    .train_data_byte_o          (train_data_byte),          // (out,8)
    .train_data_byte_valid_o    (train_data_byte_valid),    // (out,1)
    .train_slf_wr_data_o        (),                         // (out,DATA_WIDTH_IN_BITS_I) to FIFO 
    .train_slf_data_valid_o     (),                         // (out,1)                    to FIFO 
    .phylink_training_done_o    (phylink_training_done)     // (out,1) back to DevManage_fsm
);

// -----------------------------------------------------------------------------
// FrameManager_fsm — build data/control bytes per your frame format
// -----------------------------------------------------------------------------
FrameManager_fsm #(
    .WriteBufferSizeByte        (WriteBufferSizeByte),                   
    .FrameHeadEnd               (FrameHeadEnd),       
    .TOTAL_LVDS_DATA_LANES_I    (TOTAL_LVDS_DATA_LANES),
    .LVDS_TX_GEAR_RATIO         (LVDS_TX_GEAR_RATIO)
   
) u_FrameManager_fsm (
    .clk                        (fxn_gpif_m_clk),         // (in,1)
    .reset_n                    (rst_n),                  // (in,1)

    // Upstream payload
    .data_i                     (upstream_data_i),          // (in,128) user data bus
    .data_valid_i               (upstream_data_valid_i),    // (in,1)   user data valid

    // Controls
    .start_serving_device_pl_i  (start_serving_device_pl),  // (in,1) from DevManage_fsm
    .fxn_buffer_ready_i         (fxn_p0_buffer_ready_i),    // (in,1) DMA/Buffer ready 

    // Registers (I2C)
    .thread_addr_i              (thread_addr_i),            // (in,2)
    .socket_addr_i              (socket_addr_i),            // (in,3)
    .ResetOrStop_stream_req_i   (ResetOrStop_stream_req_i), // (in,1)

    // Outputs toward DataNCtrl_Send
    .dataWr_ready_o             (dataWr_ready_o),           // (out,1) 
    .dp_data_byte_o             (dp_data_byte),             // (out,128)
    .dp_data_byte_valid_o       (dp_data_byte_valid),       // (out,1)
    .dp_ctrl_byte_o             (dp_ctrl_byte),             // (out,8)
    .dp_ctrl_byte_valid_o       (dp_ctrl_byte_valid)        // (out,1)
);

// -----------------------------------------------------------------------------
// DataNCtrl_Send — merge training/data paths, handle lane ordering
// -----------------------------------------------------------------------------
DataNCtrl_Send #(
    .LVDS_TX_GEAR_RATIO_I           (LVDS_TX_GEAR_RATIO),
    .TOTAL_LVDS_DATA_LANES_I        (TOTAL_LVDS_DATA_LANES),
    .ACTIVE_LVDS_DATA_LANE_MASK_MSB_I(15),
    //1 is fx10msb first, FPGA Serializer needs LSBit first and FX10 needs MSBit first
    .TX_SERIALIZER_LSB_FIRST_I      (TX_SERIALIZER_LSB_FIRST_I) //
   
) u_DataNCtrl_Send (
    .clk                        (fxn_gpif_m_clk),         // (in,1)
    .reset_n                    (rst_n),                  // (in,1)

    // Frame data/control
    .dp_ctrl_byte_i             (dp_ctrl_byte),             // (in,8)
    .dp_ctrl_byte_valid_i       (dp_ctrl_byte_valid),       // (in,1)
    .dp_data_byte_i             (dp_data_byte),             // (in,128)
    .dp_data_byte_valid_i       (dp_data_byte_valid),       // (in,1)

    // Training data/control
    .train_ctrl_byte_i          (train_ctrl_byte),          // (in,8)
    .train_ctrl_byte_valid_i    (train_ctrl_byte_valid),    // (in,1)
    .train_data_byte_i          (train_data_byte),          // (in,8)
    .train_data_byte_valid_i    (train_data_byte_valid),    // (in,1)

    // Slave FIFO read side (for training/self data) 
    .slfifo_rd_data_i           (0),         // (in,128)
    .slfifo_rd_data_valid_i     (0),   // (in,1)

    // Output toward serializer
    .ctrl_byte_o                (ctrl_byte_8b),             // (out,8)  control byte @ 1/8 rate
    .wr_data_o                  (wr_data_128b)              // (out,128)payload bytes (16 lanes x 8)
);

// -----------------------------------------------------------------------------
// FxnTxLvds — parallel-to-serial LVDS (pin-level wrapper)
// -----------------------------------------------------------------------------
FxnTxLvds u_FxnTxLvds (
    .DataClk_DataRate_1_8       (clk_DataRate_1_8), // (in,1)  LVDS clock @ 1/8 rate (TBD)
    .data_i                     (wr_data_128b),         // (in,128)lane15..lane0 (LSB FIRST)
    .control_i                  (control_16b),           // (in,16) {ctl1,ctl0} (mapping TBD)
    .rst                        (~rst_n),              // (in,1)  active-high reset to SERDES
    .clk_DataRate_1_2           (clk_DataRate_1_2),     // (in,1)  LVDS clock @ 1/2 rate (TBD)

    .lvdsClk_DataRate_1_8       (lvdsClk_DataRate_1_8), // (out,1) optional LVDS clk out (diff pairs external)
    .dout_to_pins_ctrl          (dout_to_pins_ctrl),   // (out,2) LVDS control pins (diff pairs external)
    .dout_to_pins_data          (dout_to_pins_data)    // (out,16)LVDS data pins (diff pairs external)
);

endmodule
