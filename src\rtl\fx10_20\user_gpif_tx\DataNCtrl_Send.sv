`timescale 1ns / 1ps
//-------------------------------------------------------------------------------------------------------------------------------
//Copyright (c)  All rights reserved 
//-------------------------------------------------------------------------------------------------------------------------------
//Author : QHYCCD YangSK
//File   : DataNCtrl_Send
//Create : 2025-08-28 
//Revise : 2025-08-28 
//Editor : Vscode, tab size (4)
//VERSION: 
//Devices: 
//Functions: 
//Additional Comments:
//
//-------------------------------------------------------------------------------------------------------------------------------

module DataNCtrl_Send
  #
  (
   // Gear Ratio for LVDS
   parameter LVDS_TX_GEAR_RATIO_I = 4'd8,
  
   // LVDS Data Lanes
   parameter TOTAL_LVDS_DATA_LANES_I = 16,

   // Active LVDS Data Lane Mask MSBit
   parameter ACTIVE_LVDS_DATA_LANE_MASK_MSB_I = 15,


   // FPGA Serializer needs LSBit first and FX10 needs MSBit first
   parameter TX_SERIALIZER_LSB_FIRST_I = 1
  )
 (
  input               clk,
  input               reset_n,

  // Inputs
  input        [ LVDS_TX_GEAR_RATIO_I-1:0]  dp_ctrl_byte_i,
  input                                     dp_ctrl_byte_valid_i,
  input      [ TOTAL_LVDS_DATA_LANES_I*LVDS_TX_GEAR_RATIO_I-1:0] dp_data_byte_i,
  input                                     dp_data_byte_valid_i,
  input        [ LVDS_TX_GEAR_RATIO_I-1:0]  train_ctrl_byte_i,
  input                                     train_ctrl_byte_valid_i,
  input        [ LVDS_TX_GEAR_RATIO_I-1:0]  train_data_byte_i,
  input                                     train_data_byte_valid_i,
  input       [(TOTAL_LVDS_DATA_LANES_I * 8)-1:0] slfifo_rd_data_i,
  input                                           slfifo_rd_data_valid_i,

  // Outputs
  output logic [(LVDS_TX_GEAR_RATIO_I-1):0] ctrl_byte_o,
  output logic [TOTAL_LVDS_DATA_LANES_I*LVDS_TX_GEAR_RATIO_I-1:0] wr_data_o

 );

// ------------------------------------------
//
// Parameters
//
// ------------------------------------------
  localparam NO_OF_CLK_REQ_FOR_BYTE = ( 4'd8 / LVDS_TX_GEAR_RATIO_I );
  // IDLE Control Byte
  localparam [ 7:0] IDLE_CONTROL_BYTE = 8'd1;

// ------------------------------------------
//
// Local Variable
//
// ------------------------------------------

  logic [LVDS_TX_GEAR_RATIO_I*TOTAL_LVDS_DATA_LANES_I:0] wr_data;

  logic [LVDS_TX_GEAR_RATIO_I-1:0] ctrl_byte;
 
// MS bit of control byte is an odd parity.
// Here, bit wise XOR operator is used to generate odd parity.
// Inverted bit is the odd parity.
always_ff @ ( posedge clk )
  begin
    if ( !reset_n )
      begin
        ctrl_byte <= IDLE_CONTROL_BYTE;
      end
    else
      begin
        case ( 1'b1 )

          dp_ctrl_byte_valid_i    : ctrl_byte <= { ~(^dp_ctrl_byte_i[6:0]),  dp_ctrl_byte_i[6:0]  };
          train_ctrl_byte_valid_i : ctrl_byte <= { train_ctrl_byte_i[7:0] }; // No need to calculate parity in phy and link training         

          default               : ctrl_byte <= IDLE_CONTROL_BYTE;

        endcase
      end
  end

    // Control Byte
      always_ff @ ( posedge clk )
        begin
          if ( !reset_n )
            begin
              ctrl_byte_o <= IDLE_CONTROL_BYTE;
            end
          else if ( TX_SERIALIZER_LSB_FIRST_I == 1'b0 )
            begin
              ctrl_byte_o <= ctrl_byte;
            end
          else
            begin
              ctrl_byte_o <= {ctrl_byte[0], ctrl_byte[1], ctrl_byte[2], ctrl_byte[3],
                              ctrl_byte[4], ctrl_byte[5], ctrl_byte[6], ctrl_byte[7]};
            end
        end
    


always_ff @ ( posedge clk )
  begin
    if ( !reset_n )
      begin
        wr_data <= {TOTAL_LVDS_DATA_LANES_I{IDLE_CONTROL_BYTE}};
      end
    else
      begin

        case ( 1'b1 )

          train_data_byte_valid_i   : wr_data <= {TOTAL_LVDS_DATA_LANES_I{train_data_byte_i}}; // During PHY training, this will take care of data.
          dp_data_byte_valid_i      : wr_data <= dp_data_byte_i;  
          slfifo_rd_data_valid_i    : wr_data <= slfifo_rd_data_i;   

          default                 : wr_data <= {TOTAL_LVDS_DATA_LANES_I{IDLE_CONTROL_BYTE}};

        endcase

      end
  end

// MSB to LSB Conversion
// Some FPGA LVDS TX HIP transmits LSBit first and some MSBit first.
// FX10/20 requires the MSBit first.

  always_ff @ ( posedge clk )
            begin
              if ( !reset_n )
                begin
                  wr_data_o <= {TOTAL_LVDS_DATA_LANES_I{IDLE_CONTROL_BYTE}};
                end
              else if ( TX_SERIALIZER_LSB_FIRST_I == 1'b0 )
                begin
                  wr_data_o<= wr_data;
                end
              else
                begin
                  wr_data_o<= {<<1{wr_data}};
                end
            end
 


endmodule
