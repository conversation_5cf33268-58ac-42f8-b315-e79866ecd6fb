`timescale 1ns / 1ps
//-------------------------------------------------------------------------------------------------------------------------------
//Copyright (c)  All rights reserved 
//-------------------------------------------------------------------------------------------------------------------------------
//Author : QHYCCD YangSK
//File   : FrameManager_fsm
//Create : 2025-09-01 
//Revise : 2025-09-01 
//Editor : Vscode, tab size (4)
//VERSION: 
//Devices: 
//Functions: 
//Additional Comments:
//
//-------------------------------------------------------------------------------------------------------------------------------


// Include the package file
//`include "package/user_define_pkg.sv"


module FrameManager_fsm
  #  
  (
   parameter        WriteBufferSizeByte = 64512,
   parameter [63:0] FrameHeadEnd = 64'hEE11DD11223344,
   parameter        TOTAL_LVDS_DATA_LANES_I = 16,
   parameter        LVDS_TX_GEAR_RATIO = 8
 
  )
 (
  input               clk,
  input               reset_n,
  input logic [TOTAL_LVDS_DATA_LANES_I*LVDS_TX_GEAR_RATIO-1:0] data_i,
  input               data_valid_i,

  // Inputs
  input               start_serving_device_pl_i,             // From Device Selection Module
  input               fxn_buffer_ready_i,
  
  // Registers
  input logic [ 1:0]  thread_addr_i,
  input logic [ 2:0]  socket_addr_i,
  input               ResetOrStop_stream_req_i,

  // Outputs
  output logic        dataWr_ready_o,
  output logic [TOTAL_LVDS_DATA_LANES_I*LVDS_TX_GEAR_RATIO-1:0]dp_data_byte_o ,
  output logic        dp_data_byte_valid_o,
  output logic [ LVDS_TX_GEAR_RATIO-1:0] dp_ctrl_byte_o,
  output logic        dp_ctrl_byte_valid_o


 );

// ------------------------------------------
//
// Parameters
//
// ------------------------------------------
  localparam NO_OF_CLK_REQ_FOR_BYTE = ( 4'd8 / LVDS_TX_GEAR_RATIO );

  localparam [15:0] NO_OF_DATA_PER_PKT = WriteBufferSizeByte*8/TOTAL_LVDS_DATA_LANES_I*LVDS_TX_GEAR_RATIO-1;
  // Parity bit initial value
  localparam [0:0] PARITY_BIT = 1'b0;

  // LSbit of Control Byte
  localparam [0:0] CONTROL_BYTE_LS_BIT = 1'b1;

  // IDLE Control Byte
  localparam [7:0] IDLE_CONTROL_BYTE = 8'd1;

  // EOP Control Byte
  localparam [7:0] EOP_CONTROL_BYTE = 8'b1110_1111;

  // Set Thread Address
  localparam [3:0] THREAD_ADDR_CONTROL_BYTE = 4'b0001;

  // Set Socket Address
  localparam [2:0] SOCKET_ADDR_CONTROL_BYTE = 3'b001;


  // Full Word Data Byte send is 16 byte.
  localparam [7:0] FULL_WORD_DATA_BYTE=8'H80 ;

  // Adress to FLAGA assertion delay
  // Why 10 clocks?
  // After sending thread and socket address from
  // this FSM, it takes around 4 clocks to be on
  // control lane. And after that it may take 3
  // clocks to update FLAGA status. For safer side,
  // here, it is taken 10.
  localparam ADDR_TO_FLAGA_DELAY_CLK = 50;

  

  // No of States
  localparam NO_OF_FSM_STATES = 8'd14;

  enum logic [(NO_OF_FSM_STATES-1):0] {
    IDLE                           = 14'h1,
    //IS_SEL_DEV_ALREADY_SERVED      = 14'h2,
    SET_THREAD_ADDR                = 14'h4,
    SET_SOCKET_ADDR                = 14'h8,
    WAIT_BUFFER_AVAILABLE          = 14'h10,
    //INIT_THREAD_SEQ                = 14'h20,
    //WAIT_INIT_THREAD_SEQ_COMPLETED = 14'h40,
   
    //TX_DATA_PKT                    = 14'h200,
    WAIT_DATA_PKT_SENT             = 14'h400,
    TX_EOP                         = 14'h800,
    FULL_PKT_COMPLETED           = 14'h2000
  } fr_m_cs, fr_m_ns;

logic                           FxnDp_fifo_ren = 0;
logic                           FxnDp_fifo_aufull;
logic [TOTAL_LVDS_DATA_LANES_I*LVDS_TX_GEAR_RATIO-1:0] FxnDp_fifo_dout;
logic                           FxnDp_fifo_aempty;
logic                           FxnDp_fifo_empty;
logic                           FxnDp_fifo_rvaild;
logic [8:0]                     FxnDp_fifo_wrusedw;

// ------------------------------------------
//
// Local Variables
//
// ------------------------------------------

  //logic        skip_thread_init_seq;
  logic [ 7:0] flaga_sample_count;
  //logic        tx_thread_init_seq_pl;
  //logic        tx_data_pkt_pl;
  logic        tx_thead_addr_pl;
  logic        tx_eop_fg;
  logic        tx_socket_addr_pl;
  logic        incr_flaga_sample_count;
  logic [ 3:0] bit_counter;

  logic        data_packet_sent_done;
  logic        FrameHeadEnd_flag;
  logic [15:0] TxDataPkt_cnt=0;
  
always_comb dataWr_ready_o=FxnDp_fifo_aufull;
// ------------------------------------------
//
// FSM
//
// ------------------------------------------

// Current State
always_ff @ ( posedge clk )
  begin
    if ( !reset_n )
      begin
        fr_m_cs <= IDLE;
      end
    else if ( ResetOrStop_stream_req_i )
      begin
        fr_m_cs <= TX_EOP;
      end
    else
      begin
        fr_m_cs <= fr_m_ns;
      end
  end

// Next State
always_comb
  begin
   fr_m_ns = IDLE;
    unique case ( fr_m_cs )

      // Wait until Device manage FSM informs to start serving
      IDLE :
        begin
          if ( /* ( lvds_phy_n_link_training_done == 1'b1 ) & */
               ( start_serving_device_pl_i == 1'b1 )
             )
            begin
              fr_m_ns = SET_THREAD_ADDR;//IS_SEL_DEV_ALREADY_SERVED;
            end
          else
            begin
              fr_m_ns = IDLE;
            end
        end


      // Send thread address for selected device
      SET_THREAD_ADDR :
        begin
          if ( bit_counter == ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) )
            begin
          fr_m_ns = SET_SOCKET_ADDR;
        end
          else
            begin
              fr_m_ns = SET_THREAD_ADDR;
            end
        end

      // Initialize socket address
      // No need to wait until READY flag is de-asserted
      // as FW is intializing GPIF SM that's why READY flag is toggling
      SET_SOCKET_ADDR :
        begin
          if ( bit_counter == ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) )
            begin
          fr_m_ns = WAIT_BUFFER_AVAILABLE;
        end
          else
            begin
              fr_m_ns = SET_SOCKET_ADDR;
            end
        end

      // Wait for "ADDR_TO_FLAGA_DELAY_CLK" clocks to sample FLAGA
      WAIT_BUFFER_AVAILABLE :
        begin
          if ( flaga_sample_count == ADDR_TO_FLAGA_DELAY_CLK )
            begin
              if ( fxn_buffer_ready_i  )//& !slfifo_almost_empty_i
                begin
              fr_m_ns = WAIT_DATA_PKT_SENT;//TX_FRAME_HEADER_INFO;
             end
              else // No need to serve selected device. 
                begin
                  fr_m_ns = WAIT_BUFFER_AVAILABLE;//FULL_PKT_COMPLETED;
                end
            end
          else
            begin
              fr_m_ns = WAIT_BUFFER_AVAILABLE;
            end
        end

     
      // // Transmit data packets
      // TX_DATA_PKT :
      //   begin
      //     fr_m_ns = WAIT_DATA_PKT_SENT;
      //   end

      // Wait until full frame is sent
      WAIT_DATA_PKT_SENT :
        begin
        
          if ( data_packet_sent_done==1'b1)
            begin
              fr_m_ns = FULL_PKT_COMPLETED;
            end
          else if(FrameHeadEnd_flag==1'b1)//FxnDp_fifo_dout[127:64]==FrameHeadEnd&&FxnDp_fifo_rvaild==1
          
          begin
              fr_m_ns = TX_EOP;
            end
          else
            begin
              fr_m_ns = WAIT_DATA_PKT_SENT;
            end
        end
      TX_EOP:
        begin
         // if(dp_ctrl_byte_o[6:0]==EOP_CONTROL_BYTE[6:0])
            fr_m_ns = FULL_PKT_COMPLETED;
          //else
         //   fr_m_ns = TX_EOP;
        end

      // Current device is served. Move to next device.
      FULL_PKT_COMPLETED :
        begin
          fr_m_ns = SET_THREAD_ADDR;
        end
    default:begin
      fr_m_ns = IDLE;
    end
    endcase
  end


// Next State Signals
always_comb
  begin

   
    //tx_data_pkt_pl          = 1'b0; 
    tx_eop_fg           = 1'b0;   
    tx_thead_addr_pl        = 1'b0;
    tx_socket_addr_pl       = 1'b0;
    incr_flaga_sample_count = 1'b0;
    FxnDp_fifo_ren          =0;

    unique case ( fr_m_cs )

      // Wait until LVDS PHY and link is trained.
      IDLE :
        begin
         
        end
      // Initialize thread address
      SET_THREAD_ADDR :
        begin
          tx_thead_addr_pl = (bit_counter == 1'b0);
        end

      // Initialize socket address
      SET_SOCKET_ADDR :
        begin
          tx_socket_addr_pl = (bit_counter == 1'b0);
        end

      // Wait for "ADDR_TO_FLAGA_DELAY_CLK" clocks to sample FLAGA
      WAIT_BUFFER_AVAILABLE :
        begin
         
          incr_flaga_sample_count = 1'b1;
        end

     
      // // Transmit data packets
      // TX_DATA_PKT :
      //   begin
      //     tx_eop_fg    = 1'b0;
      //     tx_data_pkt_pl= ~FxnDp_fifo_empty;
      //     FxnDp_fifo_ren= ~FxnDp_fifo_empty;
      //   end

      // Wait until full frame is sent
      WAIT_DATA_PKT_SENT :
        begin
         
          FxnDp_fifo_ren= ~FxnDp_fifo_empty;
        end
      TX_EOP:
        begin
          tx_eop_fg    = 1'b1;
          
        end

      // Current device is served. Move to next device.
      FULL_PKT_COMPLETED :
        begin
     
        end

    default : begin
     
    end
    endcase
  end

always_comb begin 
  dp_data_byte_o=FxnDp_fifo_dout;
  dp_data_byte_valid_o=FxnDp_fifo_rvaild;
end


always_ff @( posedge clk ) begin 
  if(fr_m_cs==WAIT_DATA_PKT_SENT) begin
    if(FxnDp_fifo_ren)  begin
      TxDataPkt_cnt<=TxDataPkt_cnt+1;
    end else begin
      TxDataPkt_cnt<=TxDataPkt_cnt;
    end 
  end else begin
    TxDataPkt_cnt<=0;
  end
end


//data_packet_sent_done
always_comb begin 
  if(TxDataPkt_cnt==NO_OF_DATA_PER_PKT&&FxnDp_fifo_ren==1)
    data_packet_sent_done=1;
  else 
    data_packet_sent_done=0;
end
//data_packet_sent_done

always_comb begin
  if(FxnDp_fifo_dout[127:64]==FrameHeadEnd&&FxnDp_fifo_rvaild==1)
    FrameHeadEnd_flag=1;
  else 
    FrameHeadEnd_flag=0;
end
//frameHeadEnd_flag

// ----------------------------------------------
//
// Wait before sampling FLAGA
//
// ----------------------------------------------

// FLAGA may take 3 or 4 clocks to get updated
// once new address is available. Here, after
// 10 clock cycles, FLAGA will be sampled.
always_ff @ ( posedge clk )
  begin
    if ( ~reset_n )
      begin
        flaga_sample_count <= 8'd0;
      end
    else if ( incr_flaga_sample_count == 1'b0 )
      begin
        flaga_sample_count <= 8'd0;
      end
    else
      begin
        flaga_sample_count <= flaga_sample_count + 1'b1;
      end
  end


// ------------------------------------------
//
// Control Byte and Valid Signal
//
// ------------------------------------------

// Control Byte
always_ff @ ( posedge clk )
  begin
    if ( !reset_n )
      begin
        dp_ctrl_byte_o <= 8'd0;
      end
    else
      begin  
        case ( 1'b1 ) // Parallel case. All conditions are mutually exclusive.

          tx_thead_addr_pl  : dp_ctrl_byte_o <= { PARITY_BIT, THREAD_ADDR_CONTROL_BYTE, thread_addr_i, CONTROL_BYTE_LS_BIT };

          tx_socket_addr_pl : dp_ctrl_byte_o <= { PARITY_BIT, SOCKET_ADDR_CONTROL_BYTE, socket_addr_i, CONTROL_BYTE_LS_BIT };

          FxnDp_fifo_rvaild : dp_ctrl_byte_o <= FULL_WORD_DATA_BYTE;

          tx_eop_fg         : dp_ctrl_byte_o <= EOP_CONTROL_BYTE; 

          default           : dp_ctrl_byte_o <= IDLE_CONTROL_BYTE;

        endcase
      end
  end

// Control Byte Valid
always_ff @ ( posedge clk )
  begin
    if ( !reset_n )
      begin
        dp_ctrl_byte_valid_o <= 1'b0;
      end
    else
      begin
        dp_ctrl_byte_valid_o <= ( tx_thead_addr_pl | tx_socket_addr_pl |FxnDp_fifo_rvaild |tx_eop_fg);
      end
  end

// Bit Counter
always_ff @ ( posedge clk )
  begin
    if ( !reset_n )
      begin
        bit_counter <= 3'd0;
      end
    else if ( bit_counter == ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) )
      begin
        bit_counter <= 3'd0;
      end
    else if ( ( tx_thead_addr_pl | tx_socket_addr_pl ) |
              ( bit_counter != 1'b0 )
            )
      begin
        bit_counter <= bit_counter + 1'b1;
      end
  end

  //============================================================================//



// ------------------------------------------
//
// FxnDp FIFO Instance
//
// ------------------------------------------

 FxnDp_fifo FxnDp_fifo_inst
(
  .di           (data_i),//input   [127:0]               
  .clkr         (clk),//input                         
  .rrst         (~reset_n),//input                         
  .re           (FxnDp_fifo_ren),//input                         
  .clkw         (clk),//input                         
  .wrst         (~reset_n),//input                         
  .we           (data_valid_i),//input                         

  .dout         (FxnDp_fifo_dout),//output  [127:0]               
  .empty_flag   (FxnDp_fifo_empty),//output                        
  .aempty       (FxnDp_fifo_aempty),//output                        
  .full_flag    (),//output                        
  .afull        (FxnDp_fifo_aufull),//output                        
  .valid        (FxnDp_fifo_rvaild),//output                        
  .overflow     (),//output                        
  .underflow    (),//output                        
  .wr_success   (),//output                        
  .rdusedw      (),//output  [8:0]                 
  .wrusedw      (FxnDp_fifo_wrusedw),//output  [8:0]                 
  .wr_rst_done  (),//output                        
  .rd_rst_done  () //output 
                       
);

endmodule