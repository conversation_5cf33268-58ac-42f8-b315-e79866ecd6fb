`timescale 1ns / 1ps
//-------------------------------------------------------------------------------------------------------------------------------
//Copyright (c)  All rights reserved 
//-------------------------------------------------------------------------------------------------------------------------------
//Author : QHYCCD YangSK
//File   : lvds_phy_n_link_training_fsm
//Create : 2025-08-28 
//Revise : 2025-08-28 
//Editor : Vscode, tab size (4)
//VERSION: 
//Devices: 
//Functions: 
//Additional Comments:
//
//-------------------------------------------------------------------------------------------------------------------------------
// History:
// 2025-08-28  QHYCCD YangSK  Create
// Include the package file
//`include "package/user_define_pkg.sv"


module LvdsTraining_fsm
  #
  (
   parameter LVDS_TX_GEAR_RATIO = 8,

   // Clock Value
   parameter GPIF_M_CLK_VALUE_HZ = 75_000_000,//HZ

   // GPIF Master Mode: LVDS or LVCMOS
   parameter GPIF_MASTER_MODE_I = "LVCMOS_DDR",

   // Slave FIFO Data Width in Bits
   parameter DATA_WIDTH_IN_BITS_I = 32,

   // LVDS PHY Training Interval in micro seconds
   parameter LVDS_PHY_TS_TIME_IN_US_I = 50,

   // No of Link Training Count
   parameter LINK_TRAINING_COUNT_I = 10,

   // IDLE Interval in micro seconds after training completes
   parameter IDLE_TIME_AFTER_TRAINING_IN_US_I = 3
  )
 (
  input               clk,
  input               reset_n,
  // Inputs
  input                                     start_phylink_training_i,
  input      [7:0]                          phy_training_seq,
  input      [7:0]                          link_training_seq_p0,
  input      [7:0]                          link_training_seq_p1,
  input      [7:0]                          link_training_seq_p2,
  input      [7:0]                          link_training_seq_p3,
  // Outputs
  output        [ LVDS_TX_GEAR_RATIO-1:0]   train_ctrl_byte_o,
  output logic                              train_ctrl_byte_valid_o,
  output logic  [ LVDS_TX_GEAR_RATIO-1:0]   train_data_byte_o,
  output logic                              train_data_byte_valid_o,
  output logic [(DATA_WIDTH_IN_BITS_I-1):0] train_slf_wr_data_o,
  output logic                              train_slf_data_valid_o,
  output logic                              phylink_training_done_o

  // Registers
  
 );

// ------------------------------------------
//
// Parameters
//
// ------------------------------------------

  localparam NO_OF_CLK_REQ_FOR_BYTE = ( 4'd8 / LVDS_TX_GEAR_RATIO );
  // Clock in MHz
  localparam       GPIF_M_CLK_VALUE_IN_MHZ = GPIF_M_CLK_VALUE_HZ / 1000_000;

  // PHY Training Counter Maximum Value
  localparam       LVDS_PHY_COUNTER_MAX_VALUE = GPIF_M_CLK_VALUE_IN_MHZ * LVDS_PHY_TS_TIME_IN_US_I;

  // Link Training Counter Maximum Value
  localparam       LVDS_LINK_TRAINING_COUNTER_MAX_VALUE = LINK_TRAINING_COUNT_I * 4;

  // Control Byte for Link Trining
  localparam [7:0] LINK_TRAINING_CONTROL_BYTE = 8'hF1; // Odd parity: MSb is the parity bit

  // Number of IDLE conrol bytes required after training
  localparam       NO_OF_IDLE_CONTROL_BYTES_REQ_AFTER_TRAINING = GPIF_M_CLK_VALUE_IN_MHZ * IDLE_TIME_AFTER_TRAINING_IN_US_I;

  // IDLE Control Byte
  localparam       IDLE_CONTROL_BYTE = 8'd1;
  // No of States
  localparam       NO_OF_FSM_STATES = 8'd5;


// Number of clocks required to transmit a byte
  
 
  // FSM States
  enum logic [(NO_OF_FSM_STATES-1):0] {

    IDLE                             = 5'h1,
    START_LVDS_PHY_TRAINING          = 5'h2,
    START_LINK_TRAINING              = 5'h4,
    TX_IDLE_CTRL_BYTES               = 5'h8,
    INFORM_TRAINING_COMPLETED        = 5'h10

  } train_m_cs, train_m_ns;


// ------------------------------------------
//
// Local Variables
//
// ------------------------------------------

  logic          phy_counter_en;
  logic          link_training_counter_en;
  logic          phylink_training_done;
  logic          tx_idle_control_byte;
  logic          train_data_wr_en;
  logic  [ 32:0] lvds_phy_counter;
  logic  [  7:0] link_training_counter;
  logic  [ 15:0] idle_ctrl_byte_counter;
  logic  [  3:0] train_data_wr_count;

// ------------------------------------------
//
// FSM
//
// ------------------------------------------

// Current State
always_ff @ ( posedge clk or negedge reset_n )
  begin
    if ( !reset_n )
      begin
        train_m_cs <= IDLE;
      end
    else
      begin
        train_m_cs <= train_m_ns;
      end
  end

// Next State
always_comb
  begin
    case ( train_m_cs )

      // Device Selection FSM asks to start training.
      IDLE :
        begin
          if ( start_phylink_training_i == 1'b1 )
            begin
              if ( ( GPIF_MASTER_MODE_I == "LVDS" ) 
              //&(lvds_phy_training_en == 1'b1 )
                 )
                begin
                  train_m_ns = START_LVDS_PHY_TRAINING;
                end
              else
                begin
                  train_m_ns = START_LINK_TRAINING;
                end
            end
          else
            begin
              train_m_ns = IDLE;
            end
        end

      // Wait until PHY training is completed
      START_LVDS_PHY_TRAINING :
        begin
          if ( ( lvds_phy_counter == ( LVDS_PHY_COUNTER_MAX_VALUE - 1'b1 ) ) & phy_counter_en )
            begin
              train_m_ns = START_LINK_TRAINING;
            end
          else
            begin
              train_m_ns = START_LVDS_PHY_TRAINING;
        end
        end

      // Wait until link training is completed
      START_LINK_TRAINING :
        begin
          if ( ( link_training_counter == ( LVDS_LINK_TRAINING_COUNTER_MAX_VALUE - 1'b1 ) ) & link_training_counter_en )
            begin
              train_m_ns = TX_IDLE_CTRL_BYTES;
            end
          else
            begin
              train_m_ns = START_LINK_TRAINING;
            end
        end

      // Send IDLEs after completion of training.
      TX_IDLE_CTRL_BYTES :
        begin
          if ( ( idle_ctrl_byte_counter == ( NO_OF_IDLE_CONTROL_BYTES_REQ_AFTER_TRAINING - 1'b1 ) ) & tx_idle_control_byte )
            begin
              train_m_ns = INFORM_TRAINING_COMPLETED;
            end
          else
            begin
              train_m_ns = TX_IDLE_CTRL_BYTES;
            end
        end

      // Training is completed
      INFORM_TRAINING_COMPLETED :
        begin
          train_m_ns = IDLE;
        end
        default:begin
          train_m_ns = IDLE;
        end
    endcase
  end


// Next State Signals
always_comb
  begin

    phy_counter_en                = 1'b0;
    link_training_counter_en         = 1'b0;
    phylink_training_done = 1'b0;
    tx_idle_control_byte             = 1'b0;

    case ( train_m_cs )

      // Device Selection FSM asks to start training.
      IDLE :
        begin

        end

      // Start LVDS PHY Training
      START_LVDS_PHY_TRAINING :
        begin
          phy_counter_en = ( ~( ( train_data_wr_count < ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) ) & ( train_data_wr_en ) ) );
        end

      // Start Link Training
      START_LINK_TRAINING :
        begin
          link_training_counter_en = ( ~( ( train_data_wr_count < ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) ) & ( train_data_wr_en ) ) );
        end

      TX_IDLE_CTRL_BYTES :
        begin
          tx_idle_control_byte = ( ~( ( train_data_wr_count < ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) ) & ( train_data_wr_en ) ) );
        end

      // Training is completed
      INFORM_TRAINING_COMPLETED :
        begin
          phylink_training_done = 1'b1;
        end
    endcase
  end



// ------------------------------------------
//
// PHY Training Counter
//
// ------------------------------------------

always_ff @ ( posedge clk or negedge reset_n )
  begin
    if ( !reset_n )
      begin
        lvds_phy_counter <= 32'd0;
      end
    else if ( ( lvds_phy_counter == ( LVDS_PHY_COUNTER_MAX_VALUE - 1'b1 ) ) & phy_counter_en )
      begin
        lvds_phy_counter <= 32'd0;
      end
    else if ( phy_counter_en == 1'b1 )
      begin
        lvds_phy_counter <= lvds_phy_counter + 1'b1;
      end
  end

// ------------------------------------------
//
// IDLE Control Bytes
//
// ------------------------------------------

always_ff @ ( posedge clk or negedge reset_n )
  begin
    if ( !reset_n )
      begin
        idle_ctrl_byte_counter <= 16'd0;
      end
    else if ( ( idle_ctrl_byte_counter == ( NO_OF_IDLE_CONTROL_BYTES_REQ_AFTER_TRAINING - 1'b1 ) ) & tx_idle_control_byte )
      begin
        idle_ctrl_byte_counter <= 16'd0;
      end
    else if ( tx_idle_control_byte == 1'b1 )
      begin
        idle_ctrl_byte_counter <= idle_ctrl_byte_counter + 1'b1;
      end
  end

// ------------------------------------------
//
// Link Training Counter
//
// ------------------------------------------

always_ff @ ( posedge clk or negedge reset_n )
  begin
    if ( !reset_n )
      begin
        link_training_counter <= 8'd0;
      end
    else if ( ( link_training_counter == ( LVDS_LINK_TRAINING_COUNTER_MAX_VALUE - 1'b1 ) ) & link_training_counter_en )
      begin
        link_training_counter <= 8'd0;
      end
    else if ( link_training_counter_en == 1'b1 )
      begin
        link_training_counter <= link_training_counter + 1'b1;
      end
  end


// Link training is completed &
// PHY training is going on
always_ff @ ( posedge clk or negedge reset_n )
  begin
    if ( !reset_n )
      begin
        phylink_training_done_o <= 1'b0;
        // lvds_phy_training_ongoing_o        <= 1'b0;
      end
    else
      begin
        phylink_training_done_o <= phylink_training_done;
        // lvds_phy_training_ongoing_o        <= phy_counter_en;
      end
  end


// ------------------------------------------
//
// Control Byte and Valid Signal
//
// ------------------------------------------

generate
  if ( GPIF_MASTER_MODE_I == "LVDS" )
    begin : LVDS_BLK

      // In PHY and Link Training, data lane(s) and control lane have same value.
      assign train_ctrl_byte_o = train_data_byte_o;

      // Control Byte Valid
      always_ff @ ( posedge clk or negedge reset_n )
        begin
          if ( !reset_n )
            begin
              train_ctrl_byte_valid_o <= 1'b0;
            end
          else
            begin
              train_ctrl_byte_valid_o <= (
                                        phy_counter_en        |
                                        link_training_counter_en |
                                        tx_idle_control_byte
                                       );
            end
        end

      // ------------------------------------------
      //
      // Data Byte and Valid Signal
      //
      // ------------------------------------------

      // Data Byte
      always_ff @ ( posedge clk or negedge reset_n )
        begin
          if ( !reset_n )
            begin
              train_data_byte_o <= 8'd0;
            end
          else if ( phy_counter_en )
            begin
              train_data_byte_o <= phy_training_seq;
            end
          else if ( link_training_counter_en )
            begin
              case ( link_training_counter[1:0] )

                2'd0 : train_data_byte_o <= link_training_seq_p0;

                2'd1 : train_data_byte_o <= link_training_seq_p1;

                2'd2 : train_data_byte_o <= link_training_seq_p2;

                2'd3 : train_data_byte_o <= link_training_seq_p3;

              endcase
            end
          else
            begin
              train_data_byte_o <= IDLE_CONTROL_BYTE;
            end
        end

      // Data Byte Valid
      always_ff @ ( posedge clk or negedge reset_n )
        begin
          if ( !reset_n )
            begin
              train_data_byte_valid_o <= 1'b0;
            end
          else
            begin
              train_data_byte_valid_o <= (
                                        phy_counter_en |
                                        link_training_counter_en
                                       );
            end
        end

      // These 2 signals are not used in this mode
      // assign train_slf_wr_data_o = 64'b0;
      // assign train_slf_data_valid_o = 1'b0;

    end
  else
    begin : LVCMOS_BLK

      // logic train_slf_data_valid_o;
      // logic [(DATA_WIDTH_IN_BITS_I-1):0] train_slf_wr_data_o;

      always_ff @ ( posedge clk or negedge reset_n )
        begin
          if ( !reset_n )
            begin
              train_slf_wr_data_o <= 64'd0;
            end
          else if ( link_training_counter_en )
            begin
              case ( link_training_counter[1:0] )

                2'd0 : train_slf_wr_data_o <= {8{link_training_seq_p0}};

                2'd1 : train_slf_wr_data_o <= {8{link_training_seq_p1}};

                2'd2 : train_slf_wr_data_o <= {8{link_training_seq_p2}};

                2'd3 : train_slf_wr_data_o <= {8{link_training_seq_p3}};

              endcase
            end
          else
            begin
              train_slf_wr_data_o <= {8{IDLE_CONTROL_BYTE}};
            end
        end

      // Data Byte Valid
      always_ff @ ( posedge clk or negedge reset_n )
        begin
          if ( !reset_n )
            begin
              train_slf_data_valid_o <= 1'b0;
            end
          else
            begin
              train_slf_data_valid_o <= link_training_counter_en;
            end
        end

      // These 2 signals are not used in this mode
      // wire [7:0] train_ctrl_byte_o;
      // wire [7:0] train_data_byte_o;
      // wire       train_ctrl_byte_valid_o;
      // wire       train_data_byte_valid_o;

      // assign train_ctrl_byte_o = 8'h0;
      // assign train_data_byte_o = 8'h0;
      // assign train_ctrl_byte_valid_o = 1'b0;
      // assign train_data_byte_valid_o = 1'b0;

    end
endgenerate


// ----------------------------------------------
//
// Gear Ratio Counter
//
// ----------------------------------------------

always_ff @ ( posedge clk )
  begin
    if ( ~reset_n )
      begin
        train_data_wr_en <= 1'b0;
      end
    else if ( phy_counter_en | link_training_counter_en | tx_idle_control_byte )
      begin
        train_data_wr_en <= 1'b1;
      end
    else if ( train_data_wr_count == ( NO_OF_CLK_REQ_FOR_BYTE - 1'b1 ) )
      begin
        train_data_wr_en <= 1'b0;
      end
  end

always_ff @ ( posedge clk )
  begin
    if ( ~reset_n )
      begin
        train_data_wr_count <= 3'd0;
      end
    else if ( phy_counter_en | link_training_counter_en | tx_idle_control_byte )
      begin
        train_data_wr_count <= 3'b0;
      end
    else if ( train_data_wr_en == 1'b1 )
      begin
        train_data_wr_count <= train_data_wr_count + 1'b1;
      end
  end


endmodule