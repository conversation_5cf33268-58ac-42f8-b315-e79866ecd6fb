============================================================
   Tang Dynasty, V6.0.152183
   Copyright (c) 2012-2025 Anlogic Inc.
   Executable = C:/Anlogic/TD_6.0.3_PHX_Release_152.183/bin/td.exe
   Built at =   11:19:57 Dec 11 2024
   Run by =     21545
   Run Date =   Mon Sep  1 17:33:02 2025

   Run on =     DESKTOP-UAGK2BE
============================================================
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(104)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(287)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(288)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(355)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(356)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(357)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(366)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(369)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(371)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(374)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(375)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(376)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(377)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(378)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(379)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(389)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(390)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(391)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(392)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(401)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(402)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(405)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(406)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(409)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(410)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(418)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(423)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(425)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(426)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(427)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(432)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(447)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(458)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(469)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(476)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(477)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(480)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(481)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(482)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(493)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(494)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(498)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(500)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(501)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(505)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(506)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(524)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(526)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(104)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(287)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(288)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(355)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(356)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(357)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(366)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(369)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(371)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(374)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(375)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(376)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(377)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(378)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(379)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(389)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(390)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(391)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(392)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(401)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(402)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(405)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(406)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(409)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(410)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(418)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(423)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(425)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(426)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(427)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(432)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(447)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(458)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(469)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(476)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(477)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(480)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(481)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(482)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(493)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(494)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(498)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(500)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(501)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(505)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(506)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(524)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(526)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(104)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(287)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(288)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(355)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(356)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(357)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(366)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(369)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(371)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(374)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(375)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(376)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(377)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(378)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(379)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(389)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(390)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(391)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(392)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(401)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(402)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(405)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(406)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(409)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(410)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(418)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(423)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(425)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(426)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(427)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(432)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(447)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(458)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(469)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(476)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(477)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(480)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(481)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(482)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(493)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(494)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(498)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(500)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(501)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(505)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(506)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(524)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(526)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(104)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(287)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(288)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(355)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(356)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(357)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(366)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(369)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(371)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(374)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(375)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(376)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(377)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(378)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(379)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(389)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(390)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(391)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(392)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(401)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(402)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(405)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(406)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(409)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(410)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(418)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(423)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(425)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(426)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(427)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(432)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(447)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(458)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(469)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(476)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(477)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(480)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(481)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(482)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(493)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(494)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(498)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(500)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(501)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(505)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(506)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(524)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(526)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(104)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(287)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(288)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(355)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(356)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(357)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(366)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(369)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(371)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(372)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(374)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(375)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(376)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(377)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(378)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(379)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(389)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(390)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(391)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(392)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(401)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(402)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(405)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(406)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(409)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(410)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(418)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(423)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(425)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(426)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(427)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(432)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(447)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(458)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(469)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(476)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(477)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(480)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(481)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(482)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(493)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(494)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(498)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(500)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(501)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(505)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(506)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(524)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(526)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-5007 WARNING: parameter 'LVDS_TX_GEAR_RATIO' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(62)
HDL-5007 WARNING: parameter 'GPIF_MASTER_MODE_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(63)
HDL-5007 WARNING: parameter 'TOTAL_LVDS_DATA_LANES' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(64)
HDL-5007 WARNING: parameter 'LVDS_PHY_TS_TIME_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(65)
HDL-5007 WARNING: parameter 'LINK_TRAINING_COUNT_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(66)
HDL-5007 WARNING: parameter 'IDLE_TIME_AFTER_TRAINING_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(67)
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(104)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(352)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(353)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(420)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(421)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(434)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(436)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(437)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(437)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(439)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(440)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(441)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(442)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(443)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(444)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(454)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(455)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(456)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(457)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(466)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(467)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(470)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(471)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(474)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(475)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(483)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(487)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(488)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(490)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(492)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(496)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(497)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(512)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(523)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(534)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(541)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(542)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(545)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(546)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(547)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(556)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(558)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(559)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(563)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(565)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(566)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(570)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(571)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(589)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(591)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-5007 WARNING: parameter 'LVDS_TX_GEAR_RATIO' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(63)
HDL-5007 WARNING: parameter 'GPIF_MASTER_MODE_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(64)
HDL-5007 WARNING: parameter 'TOTAL_LVDS_DATA_LANES' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(65)
HDL-5007 WARNING: parameter 'LVDS_PHY_TS_TIME_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(66)
HDL-5007 WARNING: parameter 'LINK_TRAINING_COUNT_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(67)
HDL-5007 WARNING: parameter 'IDLE_TIME_AFTER_TRAINING_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(68)
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'sys_pll_open0', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(114)
HDL-1007 : undeclared symbol 'sys_pll_open1', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(123)
HDL-1007 : undeclared symbol 'sys_pll_open2', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(124)
HDL-1007 : undeclared symbol 'sys_pll_open10', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open9', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open8', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open7', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open6', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open5', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open4', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open3', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open21', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open19', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open17', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open15', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open22', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open20', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open18', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open16', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open14', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open13', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open12', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open11', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(352)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(353)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(420)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(421)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(431)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(434)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(436)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(437)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(437)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(439)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(440)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(441)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(442)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(443)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(444)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(454)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(455)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(456)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(457)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(466)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(467)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(470)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(471)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(474)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(475)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(483)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(487)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(488)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(490)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(491)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(492)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(496)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(497)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(512)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(523)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(534)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(541)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(542)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(545)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(546)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(547)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(556)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(558)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(559)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(563)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(565)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(566)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(570)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(571)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(589)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(591)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-5007 WARNING: parameter 'LVDS_TX_GEAR_RATIO' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(63)
HDL-5007 WARNING: parameter 'GPIF_MASTER_MODE_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(64)
HDL-5007 WARNING: parameter 'TOTAL_LVDS_DATA_LANES' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(65)
HDL-5007 WARNING: parameter 'LVDS_PHY_TS_TIME_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(66)
HDL-5007 WARNING: parameter 'LINK_TRAINING_COUNT_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(67)
HDL-5007 WARNING: parameter 'IDLE_TIME_AFTER_TRAINING_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(68)
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
HDL-8007 ERROR: 'fxn_gpif_m_clk' is not a port in src/rtl/Juno_Framework.v(245)
RUN-1002 : start command "commit_param -step design"
RUN-1001 : Print Global Property
RUN-1001 : -------------------------------------------------------------
RUN-1001 :     Parameters    |   Settings   |  Default Values  |  Note  
RUN-1001 : -------------------------------------------------------------
RUN-1001 :    enable_seed    |     off      |       off        |        
RUN-1001 :    hfn_to_clock   |  1000000000  |    1000000000    |        
RUN-1001 :      message      |   standard   |     standard     |        
RUN-1001 :    qor_monitor    |      on      |        on        |        
RUN-1001 :   speedup_effort  |      0       |        0         |        
RUN-1001 :    syn_ip_flow    |     off      |       off        |        
RUN-1001 :       thread      |     auto     |       auto       |        
RUN-1001 : -------------------------------------------------------------
RUN-1001 : Print Design Property
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :       Parameters       |  Settings  |  Default Values  |  Note  
RUN-1001 : ----------------------------------------------------------------
RUN-1001 :   default_reg_initial  |    auto    |       auto       |        
RUN-1001 :    hdl_warning_level   |   normal   |      normal      |        
RUN-1001 : ----------------------------------------------------------------
HDL-1007 : analyze verilog file al_ip/ddr_phy/ddr_phy.v
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(2)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(3)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(4)
HDL-5007 WARNING: parameter '**' becomes localparam in '**' with formal parameter declaration list in al_ip/ddr_phy/ddr_phy.v(5)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(1098)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(830)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in al_ip/ddr_phy/ddr_phy.v(831)
HDL-1007 : back to file 'al_ip/ddr_phy/ddr_phy.v' in al_ip/ddr_phy/ddr_phy.v(5105)
HDL-1007 : undeclared symbol 'user_ram_rd_data', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(10557)
HDL-1007 : undeclared symbol 'user_clk0', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11147)
HDL-1007 : undeclared symbol 'user_clk1', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11148)
HDL-1007 : undeclared symbol 'user_clk2', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11149)
HDL-1007 : undeclared symbol 'user_clk3', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11150)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11368)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(11513)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_upp', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12199)
HDL-1007 : undeclared symbol 'phy2hctrl_dti_low', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(12200)
HDL-1007 : undeclared symbol 'open', assumed default net type 'wire' in al_ip/ddr_phy/ddr_phy.v(21575)
HDL-1007 : analyze verilog file al_ip/sys_pll/sys_pll.v
HDL-1007 : undeclared symbol 'sys_pll_open0', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(114)
HDL-1007 : undeclared symbol 'sys_pll_open1', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(123)
HDL-1007 : undeclared symbol 'sys_pll_open2', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(124)
HDL-1007 : undeclared symbol 'sys_pll_open10', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open9', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open8', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open7', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open6', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open5', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open4', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open3', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(125)
HDL-1007 : undeclared symbol 'sys_pll_open21', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open19', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open17', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open15', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(127)
HDL-1007 : undeclared symbol 'sys_pll_open22', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open20', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open18', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open16', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open14', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open13', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open12', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : undeclared symbol 'sys_pll_open11', assumed default net type 'wire' in al_ip/sys_pll/sys_pll.v(128)
HDL-1007 : analyze verilog file al_ip/wrddr_fifo/wrddr_fifo.v
HDL-1007 : analyze verilog file al_ip/rddr_fifo/rddr_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/LvdsFifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v' in al_ip/FXN_TX_IP_ALL/LvdsFifo/LvdsFifo/RTL/soft_fifo_bbb686ed3b4d.v(1)
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/FxnDp_fifo.v
HDL-1007 : analyze verilog file al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v
HDL-5007 WARNING: block identifier is required on this block in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(509)
HDL-1007 : back to file 'al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v' in al_ip/FXN_TX_IP_ALL/FxnDp_fifo/FxnDp_fifo/RTL/soft_fifo_3356a19f0045.v(1)
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DDr_UiMig.v
HDL-1007 : analyze verilog file src/rtl/DDR_MC/DdrPhyMc_wrapper.v
HDL-1007 : analyze verilog file src/rtl/Juno_Framework.v
HDL-1007 : undeclared symbol 'uart_rxd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(354)
HDL-1007 : undeclared symbol 'uart_txd', assumed default net type 'wire' in src/rtl/Juno_Framework.v(355)
HDL-1007 : undeclared symbol 'fpga_state1', assumed default net type 'wire' in src/rtl/Juno_Framework.v(422)
HDL-1007 : undeclared symbol 'fpga_state2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(423)
HDL-1007 : undeclared symbol 'fpga_state3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(424)
HDL-1007 : undeclared symbol 'asmi_dataout', assumed default net type 'wire' in src/rtl/Juno_Framework.v(433)
HDL-1007 : undeclared symbol 'rstn_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(436)
HDL-1007 : undeclared symbol 'i2c_xclr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(438)
HDL-1007 : undeclared symbol 'skip_check', assumed default net type 'wire' in src/rtl/Juno_Framework.v(439)
HDL-1007 : undeclared symbol 'regddr_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(439)
HDL-1007 : undeclared symbol 'is16bit', assumed default net type 'wire' in src/rtl/Juno_Framework.v(441)
HDL-1007 : undeclared symbol 'guide1_gps_ctl', assumed default net type 'wire' in src/rtl/Juno_Framework.v(442)
HDL-1007 : undeclared symbol 'guide2', assumed default net type 'wire' in src/rtl/Juno_Framework.v(443)
HDL-1007 : undeclared symbol 'guide3', assumed default net type 'wire' in src/rtl/Juno_Framework.v(444)
HDL-1007 : undeclared symbol 'guide4', assumed default net type 'wire' in src/rtl/Juno_Framework.v(445)
HDL-1007 : undeclared symbol 'ampv_enable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(446)
HDL-1007 : undeclared symbol 'gain_18', assumed default net type 'wire' in src/rtl/Juno_Framework.v(456)
HDL-1007 : undeclared symbol 'gain_19', assumed default net type 'wire' in src/rtl/Juno_Framework.v(457)
HDL-1007 : undeclared symbol 'gain_20', assumed default net type 'wire' in src/rtl/Juno_Framework.v(458)
HDL-1007 : undeclared symbol 'gain_21', assumed default net type 'wire' in src/rtl/Juno_Framework.v(459)
HDL-1007 : undeclared symbol 'isddr', assumed default net type 'wire' in src/rtl/Juno_Framework.v(468)
HDL-1007 : undeclared symbol 'xpatch', assumed default net type 'wire' in src/rtl/Juno_Framework.v(469)
HDL-1007 : undeclared symbol 'IDLE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(472)
HDL-1007 : undeclared symbol 'LoopExp', assumed default net type 'wire' in src/rtl/Juno_Framework.v(473)
HDL-1007 : undeclared symbol 'test_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(476)
HDL-1007 : undeclared symbol 'LED_control', assumed default net type 'wire' in src/rtl/Juno_Framework.v(477)
HDL-1007 : undeclared symbol 'AMPV_MANUAL', assumed default net type 'wire' in src/rtl/Juno_Framework.v(485)
HDL-1007 : undeclared symbol 'gain_mode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(489)
HDL-1007 : undeclared symbol 'ac_mipi_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(490)
HDL-1007 : undeclared symbol 'FrameNumEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(492)
HDL-1007 : undeclared symbol 'EnableBurstMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(493)
HDL-1007 : undeclared symbol 'TrigMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(494)
HDL-1007 : undeclared symbol 'DecodeMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(498)
HDL-1007 : undeclared symbol 'DDR_CLG_R', assumed default net type 'wire' in src/rtl/Juno_Framework.v(499)
HDL-1007 : undeclared symbol 'CmosCtrlMode', assumed default net type 'wire' in src/rtl/Juno_Framework.v(514)
HDL-1007 : undeclared symbol 'watchdogenable', assumed default net type 'wire' in src/rtl/Juno_Framework.v(525)
HDL-1007 : undeclared symbol 'driver_feed_dogs', assumed default net type 'wire' in src/rtl/Juno_Framework.v(536)
HDL-1007 : undeclared symbol 'asmi_rden', assumed default net type 'wire' in src/rtl/Juno_Framework.v(543)
HDL-1007 : undeclared symbol 'asmi_readstart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(544)
HDL-1007 : undeclared symbol 'asmi_reset', assumed default net type 'wire' in src/rtl/Juno_Framework.v(547)
HDL-1007 : undeclared symbol 'asmi_erasestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(548)
HDL-1007 : undeclared symbol 'asmi_wpen', assumed default net type 'wire' in src/rtl/Juno_Framework.v(549)
HDL-1007 : undeclared symbol 'remote_sel', assumed default net type 'wire' in src/rtl/Juno_Framework.v(558)
HDL-1007 : undeclared symbol 'asmi_num', assumed default net type 'wire' in src/rtl/Juno_Framework.v(560)
HDL-1007 : undeclared symbol 'remote_reconfig', assumed default net type 'wire' in src/rtl/Juno_Framework.v(561)
HDL-1007 : undeclared symbol 'asmi_writestart', assumed default net type 'wire' in src/rtl/Juno_Framework.v(565)
HDL-1007 : undeclared symbol 'TrigSignalEn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(567)
HDL-1007 : undeclared symbol 'AMPV_MODE', assumed default net type 'wire' in src/rtl/Juno_Framework.v(568)
HDL-1007 : undeclared symbol 'Xtrig_rstn', assumed default net type 'wire' in src/rtl/Juno_Framework.v(572)
HDL-1007 : undeclared symbol 'TrigModeA', assumed default net type 'wire' in src/rtl/Juno_Framework.v(573)
HDL-1007 : undeclared symbol 'asmi_divld', assumed default net type 'wire' in src/rtl/Juno_Framework.v(591)
HDL-1007 : undeclared symbol 'asmi_didata', assumed default net type 'wire' in src/rtl/Juno_Framework.v(593)
HDL-1007 : analyze verilog file src/rtl/MipiRx_Decode.v
HDL-1007 : analyze verilog file src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(896)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(897)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(920)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(921)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(945)
HDL-1007 : undeclared symbol '**', assumed default net type '**' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(946)
HDL-1007 : back to file 'src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv' in src/demo_ip/MIPI/mipi_dphy_rx_ph1a_mipiio_wrapper.sv(35)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/i2cSlave.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/i2cSlave.v' in src/rtl/I2CSLAVE/i2cSlave.v(45)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/registerInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/registerInterface.v' in src/rtl/I2CSLAVE/registerInterface.v(47)
HDL-1007 : analyze verilog file src/rtl/I2CSLAVE/serialInterface.v
HDL-1007 : analyze included file src/rtl/I2CSLAVE/timescale.v in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(55)
HDL-1007 : analyze included file src/rtl/I2CSLAVE/i2cSlave_define.v in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : back to file 'src/rtl/I2CSLAVE/serialInterface.v' in src/rtl/I2CSLAVE/serialInterface.v(56)
HDL-1007 : analyze verilog file src/rtl/fpga_info.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxLvds.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxTop.v
HDL-5007 WARNING: parameter 'LVDS_TX_GEAR_RATIO' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(63)
HDL-5007 WARNING: parameter 'GPIF_MASTER_MODE_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(64)
HDL-5007 WARNING: parameter 'TOTAL_LVDS_DATA_LANES' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(65)
HDL-5007 WARNING: parameter 'LVDS_PHY_TS_TIME_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(66)
HDL-5007 WARNING: parameter 'LINK_TRAINING_COUNT_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(67)
HDL-5007 WARNING: parameter 'IDLE_TIME_AFTER_TRAINING_IN_US_I' becomes localparam in 'FxnTxTop' with formal parameter declaration list in src/rtl/fx10_20/FxnTxTop.v(68)
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxCtrl_4to1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/FxnTxData_4TO1.v
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DataNCtrl_Send.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/DevManage_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/FrameManager_fsm.sv
HDL-1007 : analyze verilog file src/rtl/fx10_20/user_gpif_tx/LvdsTraining_fsm.sv
